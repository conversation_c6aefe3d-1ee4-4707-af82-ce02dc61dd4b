// src/config/env.validation.ts
/**
 * 环境变量验证和类型定义
 */

import { z } from 'zod';

// 环境变量验证模式
const envSchema = z.object({
  // 应用配置
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  HOST: z.string().default('0.0.0.0'),
  PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).default('3000'),
  APP_VERSION: z.string().default('1.0.0'),
  BASE_URL: z.string().url().optional(),
  API_PREFIX: z.string().default(''),
  TIMEZONE: z.string().default('Asia/Shanghai'),

  // 数据库配置
  DATABASE_URL: z.string().min(1, '数据库连接字符串不能为空'),
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).default('5432'),
  DB_USERNAME: z.string().default('postgres'),
  DB_PASSWORD: z.string().default('postgres'),
  DB_DATABASE: z.string().default('hono_admin'),

  // Redis配置
  REDIS_HOST: z.string().default('redis://localhost:6379'),
  REDIS_PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().transform(Number).pipe(z.number().min(0).max(15)).default('0'),
  REDIS_KEY_PREFIX: z.string().default('hono:admin:'),
  CACHE_ENABLED: z.string().transform(val => val === 'true').default('true'),
  CACHE_EXPIRES_IN: z.string().transform(Number).pipe(z.number().min(1)).default('3600'),

  // 认证配置
  JWT_SECRET: z.string().min(32, 'JWT密钥长度至少32位'),
  JWT_EXPIRES_IN: z.string().default('24h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  DEFAULT_PASSWORD: z.string().default('123456'),
  SALT_ROUNDS: z.string().transform(Number).pipe(z.number().min(1).max(20)).default('10'),

  // 日志配置
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_DIR: z.string().default('logs'),
  LOG_FILE_ENABLED: z.string().transform(val => val === 'true').default('true'),
  LOG_CONSOLE_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  OPERATION_LOG_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // 文件上传配置
  UPLOAD_DIR: z.string().default('uploads'),
  UPLOAD_MAX_SIZE: z.string().transform(Number).pipe(z.number().min(1)).default('10485760'), // 10MB
  UPLOAD_ALLOWED_TYPES: z.string().default('image/jpeg,image/png,image/gif,application/pdf'),

  // 邮件配置
  MAIL_HOST: z.string().optional(),
  MAIL_PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).optional(),
  MAIL_USER: z.string().optional(),
  MAIL_PASS: z.string().optional(),
  MAIL_FROM: z.string().email().optional(),

  // AI配置
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_API_KEY: z.string().optional(),
  AI_DEFAULT_MODEL: z.string().default('gpt-3.5-turbo'),
  AI_MAX_TOKENS: z.string().transform(Number).pipe(z.number().min(1)).default('2048'),
  AI_TEMPERATURE: z.string().transform(Number).pipe(z.number().min(0).max(2)).default('0.7'),

  // 安全配置
  CORS_ORIGIN: z.string().default('*'),
  CORS_CREDENTIALS: z.string().transform(val => val === 'true').default('true'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().min(1)).default('900000'), // 15分钟
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).pipe(z.number().min(1)).default('100'),
  BODY_SIZE_LIMIT: z.string().transform(Number).pipe(z.number().min(1)).default('10485760'), // 10MB

  // 监控配置
  HEALTH_CHECK_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  METRICS_ENABLED: z.string().transform(val => val === 'true').default('false'),
  PERFORMANCE_MONITORING: z.string().transform(val => val === 'true').default('false'),
});

// 环境变量类型
export type EnvConfig = z.infer<typeof envSchema>;

/**
 * 验证和解析环境变量
 */
export function validateEnv(): EnvConfig {
  try {
    const parsed = envSchema.parse(process.env);
    return parsed;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      ).join('\n');
      
      throw new Error(`环境变量验证失败:\n${errorMessages}`);
    }
    throw error;
  }
}

/**
 * 获取环境变量配置
 */
export const env = validateEnv();

/**
 * 检查必需的环境变量
 */
export function checkRequiredEnv(): void {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
}

/**
 * 获取数据库连接配置
 */
export function getDatabaseConfig() {
  return {
    url: env.DATABASE_URL,
    host: env.DB_HOST,
    port: env.DB_PORT,
    username: env.DB_USERNAME,
    password: env.DB_PASSWORD,
    database: env.DB_DATABASE,
  };
}

/**
 * 获取Redis连接配置
 */
export function getRedisConfig() {
  return {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
    keyPrefix: env.REDIS_KEY_PREFIX,
    cacheEnabled: env.CACHE_ENABLED,
    cacheExpiresIn: env.CACHE_EXPIRES_IN,
  };
}

/**
 * 获取认证配置
 */
export function getAuthConfig() {
  return {
    jwtSecret: env.JWT_SECRET,
    jwtExpiresIn: env.JWT_EXPIRES_IN,
    jwtRefreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
    defaultPassword: env.DEFAULT_PASSWORD,
    saltRounds: env.SALT_ROUNDS,
  };
}

/**
 * 获取日志配置
 */
export function getLoggerConfig() {
  return {
    level: env.LOG_LEVEL,
    dir: env.LOG_DIR,
    fileEnabled: env.LOG_FILE_ENABLED,
    consoleEnabled: env.LOG_CONSOLE_ENABLED,
    operationLogEnabled: env.OPERATION_LOG_ENABLED,
  };
}

/**
 * 获取上传配置
 */
export function getUploadConfig() {
  return {
    dir: env.UPLOAD_DIR,
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(','),
  };
}

/**
 * 获取CORS配置
 */
export function getCorsConfig() {
  return {
    origin: env.CORS_ORIGIN === '*' ? '*' : env.CORS_ORIGIN.split(','),
    credentials: env.CORS_CREDENTIALS,
  };
}

/**
 * 获取速率限制配置
 */
export function getRateLimitConfig() {
  return {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  };
}

/**
 * 是否为开发环境
 */
export const isDevelopment = env.NODE_ENV === 'development';

/**
 * 是否为生产环境
 */
export const isProduction = env.NODE_ENV === 'production';

/**
 * 是否为测试环境
 */
export const isTest = env.NODE_ENV === 'test';
