{"name": "hono-admin", "type": "module", "scripts": {"dev": "bun run --hot src/server.ts", "build": "bun build src/server.ts --outdir dist", "start": "bun run dist/server.js"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/google": "^1.2.14", "@ai-sdk/openai": "^1.3.20", "@hono/zod-validator": "^0.4.3", "@prisma/client": "^6.5.0", "@types/nodemailer": "^6.4.17", "ai": "^4.3.11", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "hono": "^4.7.5", "ioredis": "^5.6.0", "nodemailer": "^6.10.0", "prisma": "^6.5.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bun": "^1.2.10"}, "prisma": {"seed": "bun run src/db/seed.ts"}}