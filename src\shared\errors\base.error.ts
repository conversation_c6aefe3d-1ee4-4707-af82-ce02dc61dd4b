// src/shared/errors/base.error.ts
/**
 * 基础错误类定义
 */

import { ErrorCode, ErrorMessages } from '@/types/common';

/**
 * 基础业务错误类
 */
export class BaseError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: any;
  public readonly timestamp: string;

  constructor(
    code: ErrorCode,
    message?: string,
    statusCode?: number,
    details?: any
  ) {
    super(message || ErrorMessages[code]);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode || this.getDefaultStatusCode(code);
    this.details = details;
    this.timestamp = new Date().toISOString();

    // 确保错误堆栈正确
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * 根据错误码获取默认HTTP状态码
   */
  private getDefaultStatusCode(code: ErrorCode): number {
    if (code >= 1000 && code < 2000) return 400; // 用户相关错误
    if (code >= 2000 && code < 3000) return 404; // 数据相关错误
    if (code >= 3000 && code < 4000) return 500; // 系统相关错误
    
    switch (code) {
      case ErrorCode.SUCCESS:
        return 200;
      case ErrorCode.BAD_REQUEST:
        return 400;
      case ErrorCode.UNAUTHORIZED:
        return 401;
      case ErrorCode.FORBIDDEN:
        return 403;
      case ErrorCode.NOT_FOUND:
        return 404;
      case ErrorCode.INTERNAL_ERROR:
        return 500;
      default:
        return 500;
    }
  }

  /**
   * 转换为API响应格式
   */
  toApiResponse() {
    return {
      code: this.code,
      message: this.message,
      timestamp: this.timestamp,
      ...(this.details && { details: this.details }),
    };
  }
}

/**
 * 认证相关错误
 */
export class AuthError extends BaseError {
  constructor(code: ErrorCode = ErrorCode.UNAUTHORIZED, message?: string, details?: any) {
    super(code, message, 401, details);
  }
}

/**
 * 权限相关错误
 */
export class PermissionError extends BaseError {
  constructor(message?: string, details?: any) {
    super(ErrorCode.PERMISSION_DENIED, message, 403, details);
  }
}

/**
 * 验证相关错误
 */
export class ValidationError extends BaseError {
  constructor(message?: string, details?: any) {
    super(ErrorCode.BAD_REQUEST, message, 400, details);
  }
}

/**
 * 数据不存在错误
 */
export class NotFoundError extends BaseError {
  constructor(resource?: string, details?: any) {
    const message = resource ? `${resource}不存在` : undefined;
    super(ErrorCode.DATA_NOT_FOUND, message, 404, details);
  }
}

/**
 * 数据已存在错误
 */
export class ConflictError extends BaseError {
  constructor(resource?: string, details?: any) {
    const message = resource ? `${resource}已存在` : undefined;
    super(ErrorCode.DATA_ALREADY_EXISTS, message, 409, details);
  }
}

/**
 * 数据库操作错误
 */
export class DatabaseError extends BaseError {
  constructor(message?: string, details?: any) {
    super(ErrorCode.DATABASE_ERROR, message, 500, details);
  }
}

/**
 * Redis操作错误
 */
export class RedisError extends BaseError {
  constructor(message?: string, details?: any) {
    super(ErrorCode.REDIS_ERROR, message, 500, details);
  }
}

/**
 * 文件上传错误
 */
export class FileUploadError extends BaseError {
  constructor(message?: string, details?: any) {
    super(ErrorCode.FILE_UPLOAD_ERROR, message, 400, details);
  }
}

/**
 * 用户相关错误
 */
export class UserError extends BaseError {
  static notFound(username?: string) {
    return new UserError(
      ErrorCode.USER_NOT_FOUND,
      username ? `用户 ${username} 不存在` : undefined
    );
  }

  static disabled(username?: string) {
    return new UserError(
      ErrorCode.USER_DISABLED,
      username ? `用户 ${username} 已被禁用` : undefined
    );
  }

  static invalidPassword() {
    return new UserError(ErrorCode.INVALID_PASSWORD);
  }

  static captchaError() {
    return new UserError(ErrorCode.CAPTCHA_ERROR);
  }

  static tokenExpired() {
    return new UserError(ErrorCode.TOKEN_EXPIRED);
  }
}

/**
 * 错误工厂类
 */
export class ErrorFactory {
  /**
   * 创建认证错误
   */
  static auth(code: ErrorCode = ErrorCode.UNAUTHORIZED, message?: string, details?: any) {
    return new AuthError(code, message, details);
  }

  /**
   * 创建权限错误
   */
  static permission(message?: string, details?: any) {
    return new PermissionError(message, details);
  }

  /**
   * 创建验证错误
   */
  static validation(message?: string, details?: any) {
    return new ValidationError(message, details);
  }

  /**
   * 创建数据不存在错误
   */
  static notFound(resource?: string, details?: any) {
    return new NotFoundError(resource, details);
  }

  /**
   * 创建数据冲突错误
   */
  static conflict(resource?: string, details?: any) {
    return new ConflictError(resource, details);
  }

  /**
   * 创建数据库错误
   */
  static database(message?: string, details?: any) {
    return new DatabaseError(message, details);
  }

  /**
   * 创建Redis错误
   */
  static redis(message?: string, details?: any) {
    return new RedisError(message, details);
  }

  /**
   * 创建文件上传错误
   */
  static fileUpload(message?: string, details?: any) {
    return new FileUploadError(message, details);
  }

  /**
   * 从Prisma错误创建业务错误
   */
  static fromPrismaError(error: any): BaseError {
    if (error.code === 'P2002') {
      return new ConflictError('数据已存在', { constraint: error.meta?.target });
    }
    if (error.code === 'P2025') {
      return new NotFoundError('数据不存在');
    }
    return new DatabaseError('数据库操作失败', { code: error.code, message: error.message });
  }
}
