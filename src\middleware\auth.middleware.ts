import type { Context, Next } from "hono";
import { verify } from "hono/jwt";
import { redisUtils } from "@/utils/redis.utils";
import { config } from "@/config";
import { logger } from "@/utils/logger.utils";
import { prisma } from "@/db/prisma";
import { AuthError, UserError } from "@/shared/errors";
import { ErrorCode } from "@/types/common";
import type { RequestUser } from "@/types/common";

// 定义JWT负载的类型
interface JWTPayload {
  jti: string;
  userId: number;
  username: string;
  iat: number;
  exp: number;
}

/**
 * 认证中间件
 */
export async function authMiddleware(c: Context, next: Next) {
  try {
    const authHeader = c.req.header("Authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new AuthError(ErrorCode.UNAUTHORIZED, "未授权，请先登录");
    }

    const token = authHeader.split(" ")[1];

    // 验证JWT
    let payload: JWTPayload;
    try {
      payload = (await verify(
        token,
        config.auth.jwtSecret
      )) as unknown as JWTPayload;
    } catch (error) {
      logger.warn("JWT验证失败:", { error, path: c.req.path });

      if (error instanceof Error && error.message.includes("expired")) {
        throw UserError.tokenExpired();
      }
      throw new AuthError(ErrorCode.UNAUTHORIZED, "无效的token");
    }

    // 检查Redis中是否存在该token
    const redisKey = `user:${payload.userId}`;
    const storedToken = await redisUtils.get(redisKey);

    if (!storedToken || storedToken !== token) {
      throw UserError.tokenExpired();
    }

    // 查询用户信息
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        username: true,
        nickname: true,
        status: true,
        userRoles: {
          include: {
            role: {
              select: {
                key: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw UserError.notFound();
    }

    if (user.status === "1") {
      throw UserError.disabled(user.username);
    }

    // 获取用户权限
    const roles = user.userRoles.map((ur) => ur.role.key);
    const permissions = await getUserPermissions(user.id);

    // 构建请求用户信息
    const requestUser: RequestUser = {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      roles,
      permissions,
    };

    // 将用户信息存储到上下文中
    c.set("user", requestUser);

    // 检查令牌是否接近过期，自动刷新
    const now = Math.floor(Date.now() / 1000);
    const timeRemaining = payload.exp - now;
    const tokenLifetime = payload.exp - payload.iat;
    const refreshThreshold = tokenLifetime * 0.2; // 20%的剩余寿命

    if (timeRemaining < refreshThreshold) {
      // 可以在这里添加令牌刷新逻辑
      c.header("X-Token-Refresh-Needed", "true");
    }

    await next();
  } catch (error) {
    if (error instanceof AuthError || error instanceof UserError) {
      throw error;
    }
    logger.error("认证中间件错误:", error);
    throw new AuthError(ErrorCode.UNAUTHORIZED, "Token验证失败");
  }
}

/**
 * 可选认证中间件
 * 如果有token则验证，没有则跳过
 */
export async function optionalAuthMiddleware(c: Context, next: Next) {
  const authHeader = c.req.header("Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return next();
  }

  try {
    await authMiddleware(c, next);
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    return next();
  }
}

/**
 * 获取用户权限
 */
async function getUserPermissions(userId: number): Promise<string[]> {
  // 获取用户角色ID列表
  const userRoles = await prisma.userRole.findMany({
    where: { userId },
    select: { roleId: true },
  });

  const roleIds = userRoles.map((ur) => ur.roleId);

  // 管理员角色判断
  const isAdmin = await prisma.role.findFirst({
    where: {
      id: { in: roleIds },
      key: "admin",
    },
  });

  // 如果是管理员，获取所有菜单权限
  if (isAdmin) {
    const allMenus = await prisma.menu.findMany({
      where: {
        AND: [{ permission: { not: null } }, { permission: { not: "" } }],
      },
      select: { permission: true },
    });

    return allMenus.map((menu) => menu.permission).filter(Boolean) as string[];
  }

  // 非管理员，获取角色对应的菜单权限
  const roleMenus = await prisma.roleMenu.findMany({
    where: { roleId: { in: roleIds } },
    include: { menu: true },
  });

  // 提取权限标识并去重
  const permissions = roleMenus
    .map((rm) => rm.menu.permission)
    .filter(Boolean) as string[];

  return [...new Set(permissions)];
}

/**
 * 获取当前登录用户信息的辅助函数
 */
export function getCurrentUser(c: Context): RequestUser {
  return c.get("user");
}
