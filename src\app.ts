// src/app.ts
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";
import { secureHeaders } from "hono/secure-headers";
import { serveStatic } from "hono/bun";
import {
  errorMiddleware,
  notFoundMiddleware,
  operationLogMiddleware,
  requestIdMiddleware,
  requestTimeMiddleware,
  performanceMiddleware,
  createCorsMiddleware,
  securityHeadersMiddleware,
  healthCheckMiddleware,
} from "@/middleware";
import { config } from "./config";
import { createRoutes } from "./routes";

const app = new Hono();

// 全局中间件
app.use("*", requestIdMiddleware);
app.use("*", requestTimeMiddleware);
app.use("*", performanceMiddleware);
app.use("*", healthCheckMiddleware);
app.use("*", logger());
app.use("*", createCorsMiddleware(config.cors));
app.use("*", securityHeadersMiddleware);
app.use("*", prettyJSON());
app.use("*", errorMiddleware);

// 静态文件服务
app.use("/uploads/*", serveStatic({ root: "./" }));

// 操作日志记录 - 排除特定路径
app.use("*", (c, next) => {
  const path = c.req.path;
  // 排除登录、静态资源等路径的日志记录
  if (
    path.startsWith("/auth/login") ||
    path.startsWith("/uploads/") ||
    path === "/"
  ) {
    return next();
  }
  return operationLogMiddleware(c, next);
});

// 注册所有路由
app.route("/", createRoutes());

// 健康检查
app.get("/", (c) =>
  c.json({
    status: "ok",
    message: "Hono Admin API is running",
    version: config.app.version,
    timestamp: new Date().toISOString(),
  })
);

// 404处理
app.notFound(notFoundMiddleware);

export default app;
