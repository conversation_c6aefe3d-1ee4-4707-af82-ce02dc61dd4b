// src/shared/utils/crypto.util.ts
/**
 * 加密工具类
 */

import * as crypto from 'crypto';
import * as bcrypt from 'bcryptjs';

/**
 * 加密工具类
 */
export class CryptoUtil {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成UUID
   */
  static generateUUID(): string {
    return crypto.randomUUID();
  }

  /**
   * 生成随机数字
   */
  static generateRandomNumber(min: number = 0, max: number = 999999): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * MD5哈希
   */
  static md5(data: string): string {
    return crypto.createHash('md5').update(data).digest('hex');
  }

  /**
   * SHA256哈希
   */
  static sha256(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * SHA512哈希
   */
  static sha512(data: string): string {
    return crypto.createHash('sha512').update(data).digest('hex');
  }

  /**
   * HMAC签名
   */
  static hmacSha256(data: string, key: string): string {
    return crypto.createHmac('sha256', key).update(data).digest('hex');
  }

  /**
   * 密码哈希（使用bcrypt）
   */
  static async hashPassword(password: string, saltRounds: number = 10): Promise<string> {
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * 生成盐值
   */
  static async generateSalt(rounds: number = 10): Promise<string> {
    return bcrypt.genSalt(rounds);
  }

  /**
   * AES加密
   */
  static encrypt(text: string, key: string): {
    encrypted: string;
    iv: string;
    tag: string;
  } {
    // 确保密钥长度
    const keyBuffer = crypto.scryptSync(key, 'salt', this.KEY_LENGTH);
    
    // 生成随机IV
    const iv = crypto.randomBytes(this.IV_LENGTH);
    
    // 创建加密器
    const cipher = crypto.createCipherGCM(this.ALGORITHM, keyBuffer, iv);
    
    // 加密数据
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // 获取认证标签
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }

  /**
   * AES解密
   */
  static decrypt(encryptedData: {
    encrypted: string;
    iv: string;
    tag: string;
  }, key: string): string {
    // 确保密钥长度
    const keyBuffer = crypto.scryptSync(key, 'salt', this.KEY_LENGTH);
    
    // 转换参数
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');
    
    // 创建解密器
    const decipher = crypto.createDecipherGCM(this.ALGORITHM, keyBuffer, iv);
    decipher.setAuthTag(tag);
    
    // 解密数据
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Base64编码
   */
  static base64Encode(data: string): string {
    return Buffer.from(data, 'utf8').toString('base64');
  }

  /**
   * Base64解码
   */
  static base64Decode(data: string): string {
    return Buffer.from(data, 'base64').toString('utf8');
  }

  /**
   * URL安全的Base64编码
   */
  static base64UrlEncode(data: string): string {
    return Buffer.from(data, 'utf8')
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * URL安全的Base64解码
   */
  static base64UrlDecode(data: string): string {
    // 补充填充字符
    const padding = '='.repeat((4 - (data.length % 4)) % 4);
    const base64 = data.replace(/-/g, '+').replace(/_/g, '/') + padding;
    return Buffer.from(base64, 'base64').toString('utf8');
  }

  /**
   * 生成RSA密钥对
   */
  static generateRSAKeyPair(keySize: number = 2048): {
    publicKey: string;
    privateKey: string;
  } {
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: keySize,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem',
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem',
      },
    });

    return { publicKey, privateKey };
  }

  /**
   * RSA公钥加密
   */
  static rsaEncrypt(data: string, publicKey: string): string {
    const buffer = Buffer.from(data, 'utf8');
    const encrypted = crypto.publicEncrypt(publicKey, buffer);
    return encrypted.toString('base64');
  }

  /**
   * RSA私钥解密
   */
  static rsaDecrypt(encryptedData: string, privateKey: string): string {
    const buffer = Buffer.from(encryptedData, 'base64');
    const decrypted = crypto.privateDecrypt(privateKey, buffer);
    return decrypted.toString('utf8');
  }

  /**
   * RSA私钥签名
   */
  static rsaSign(data: string, privateKey: string): string {
    const sign = crypto.createSign('SHA256');
    sign.update(data);
    sign.end();
    return sign.sign(privateKey, 'base64');
  }

  /**
   * RSA公钥验证签名
   */
  static rsaVerify(data: string, signature: string, publicKey: string): boolean {
    const verify = crypto.createVerify('SHA256');
    verify.update(data);
    verify.end();
    return verify.verify(publicKey, signature, 'base64');
  }

  /**
   * 生成JWT密钥
   */
  static generateJWTSecret(length: number = 64): string {
    return crypto.randomBytes(length).toString('base64');
  }

  /**
   * 时间安全的字符串比较
   */
  static timingSafeEqual(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }
    
    const bufferA = Buffer.from(a, 'utf8');
    const bufferB = Buffer.from(b, 'utf8');
    
    return crypto.timingSafeEqual(bufferA, bufferB);
  }

  /**
   * 生成CSRF Token
   */
  static generateCSRFToken(): string {
    return this.generateRandomString(32);
  }

  /**
   * 生成API Key
   */
  static generateAPIKey(prefix: string = 'ak'): string {
    const timestamp = Date.now().toString(36);
    const random = this.generateRandomString(16);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * 生成验证码
   */
  static generateCaptcha(length: number = 6, type: 'number' | 'letter' | 'mixed' = 'mixed'): string {
    let chars = '';
    
    switch (type) {
      case 'number':
        chars = '0123456789';
        break;
      case 'letter':
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        break;
      case 'mixed':
        chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        break;
    }
    
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 计算文件哈希
   */
  static calculateFileHash(buffer: Buffer, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(buffer).digest('hex');
  }

  /**
   * 生成随机颜色
   */
  static generateRandomColor(): string {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
  }
}
