// src/shared/utils/response.util.ts
/**
 * 统一响应工具类
 */

import type { Context } from 'hono';
import type { ApiResponse, PaginatedApiResponse, PaginationResult } from '@/types/common';
import { ErrorCode, ErrorMessages } from '@/types/common';

/**
 * 响应工具类
 */
export class ResponseUtil {
  /**
   * 成功响应
   */
  static success<T>(c: Context, data?: T, message?: string): Response {
    const response: ApiResponse<T> = {
      code: ErrorCode.SUCCESS,
      message: message || ErrorMessages[ErrorCode.SUCCESS],
      data,
      timestamp: new Date().toISOString(),
    };
    return c.json(response);
  }

  /**
   * 分页成功响应
   */
  static successWithPagination<T>(
    c: Context,
    data: PaginationResult<T>,
    message?: string
  ): Response {
    const response: PaginatedApiResponse<T> = {
      code: ErrorCode.SUCCESS,
      message: message || ErrorMessages[ErrorCode.SUCCESS],
      data,
      timestamp: new Date().toISOString(),
    };
    return c.json(response);
  }

  /**
   * 错误响应
   */
  static error(
    c: Context,
    code: ErrorCode,
    message?: string,
    statusCode?: number,
    details?: any
  ): Response {
    const response: ApiResponse = {
      code,
      message: message || ErrorMessages[code],
      timestamp: new Date().toISOString(),
      ...(details && { details }),
    };
    
    const httpStatus = statusCode || this.getHttpStatusFromErrorCode(code);
    return c.json(response, httpStatus);
  }

  /**
   * 根据错误码获取HTTP状态码
   */
  private static getHttpStatusFromErrorCode(code: ErrorCode): number {
    if (code >= 1000 && code < 2000) return 400; // 用户相关错误
    if (code >= 2000 && code < 3000) return 404; // 数据相关错误
    if (code >= 3000 && code < 4000) return 500; // 系统相关错误
    
    switch (code) {
      case ErrorCode.SUCCESS:
        return 200;
      case ErrorCode.BAD_REQUEST:
        return 400;
      case ErrorCode.UNAUTHORIZED:
        return 401;
      case ErrorCode.FORBIDDEN:
        return 403;
      case ErrorCode.NOT_FOUND:
        return 404;
      case ErrorCode.INTERNAL_ERROR:
        return 500;
      default:
        return 500;
    }
  }

  /**
   * 创建分页结果
   */
  static createPaginationResult<T>(
    list: T[],
    total: number,
    pageNum: number,
    pageSize: number
  ): PaginationResult<T> {
    return {
      list,
      total,
      pageNum,
      pageSize,
      pages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 验证分页参数
   */
  static validatePaginationParams(pageNum?: number, pageSize?: number) {
    const validPageNum = Math.max(1, pageNum || 1);
    const validPageSize = Math.min(Math.max(1, pageSize || 10), 100); // 限制最大100条
    
    return {
      pageNum: validPageNum,
      pageSize: validPageSize,
      skip: (validPageNum - 1) * validPageSize,
      take: validPageSize,
    };
  }

  /**
   * 处理排序参数
   */
  static handleOrderBy(orderBy?: string, orderDirection?: 'asc' | 'desc') {
    if (!orderBy) return undefined;
    
    const direction = orderDirection === 'desc' ? 'desc' : 'asc';
    return { [orderBy]: direction };
  }
}

/**
 * 响应装饰器工厂
 */
export function ApiResponse<T = any>(message?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (c: Context, ...args: any[]) {
      try {
        const result = await originalMethod.apply(this, [c, ...args]);
        
        // 如果方法已经返回了Response，直接返回
        if (result instanceof Response) {
          return result;
        }
        
        // 否则包装成成功响应
        return ResponseUtil.success(c, result, message);
      } catch (error) {
        // 错误会被全局错误处理中间件捕获
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * 分页响应装饰器工厂
 */
export function PaginatedApiResponse<T = any>(message?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (c: Context, ...args: any[]) {
      try {
        const result = await originalMethod.apply(this, [c, ...args]);
        
        // 如果方法已经返回了Response，直接返回
        if (result instanceof Response) {
          return result;
        }
        
        // 否则包装成分页响应
        return ResponseUtil.successWithPagination(c, result, message);
      } catch (error) {
        // 错误会被全局错误处理中间件捕获
        throw error;
      }
    };
    
    return descriptor;
  };
}
