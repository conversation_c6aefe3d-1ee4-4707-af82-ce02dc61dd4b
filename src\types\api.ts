// src/types/api.ts
/**
 * API相关类型定义
 */

import type { PaginationQuery, ApiResponse, PaginatedApiResponse } from './common';
import type { User, Role, Menu, Dept, Post, DictType, DictData } from './entities';

// ============= 认证相关 =============

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
  captcha: string;
}

// 登录响应
export interface LoginResponse {
  token: string;
  expiresIn: number;
}

// 用户信息响应
export interface UserInfoResponse {
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
    email?: string;
    status: string;
  };
  roles: string[];
  permissions: string[];
}

// 路由菜单响应
export interface RouterResponse {
  id: number;
  parentId?: number;
  path?: string;
  name: string;
  component?: string;
  icon?: string;
  type: number;
  orderNo: number;
  children?: RouterResponse[];
}

// ============= 用户管理 =============

// 用户查询参数
export interface UserQuery extends PaginationQuery {
  username?: string;
  nickname?: string;
  status?: string;
  deptId?: number;
  beginTime?: string;
  endTime?: string;
}

// 用户创建请求
export interface CreateUserRequest {
  username: string;
  nickname: string;
  password?: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  status: string;
  deptId?: number;
  roleIds?: number[];
  postIds?: number[];
  remark?: string;
}

// 用户更新请求
export interface UpdateUserRequest extends Partial<CreateUserRequest> {
  id: number;
}

// 用户响应
export interface UserResponse extends Omit<User, 'password' | 'psalt'> {
  dept?: {
    id: number;
    name: string;
  };
  roles?: Array<{
    id: number;
    name: string;
    key: string;
  }>;
  posts?: Array<{
    id: number;
    name: string;
    code: string;
  }>;
}

// ============= 角色管理 =============

// 角色查询参数
export interface RoleQuery extends PaginationQuery {
  name?: string;
  key?: string;
  status?: string;
  beginTime?: string;
  endTime?: string;
}

// 角色创建请求
export interface CreateRoleRequest {
  name: string;
  key: string;
  sort: number;
  status: string;
  menuIds?: number[];
  deptIds?: number[];
  remark?: string;
}

// 角色更新请求
export interface UpdateRoleRequest extends Partial<CreateRoleRequest> {
  id: number;
}

// 角色响应
export interface RoleResponse extends Role {
  menuIds?: number[];
  deptIds?: number[];
}

// ============= 菜单管理 =============

// 菜单查询参数
export interface MenuQuery {
  name?: string;
  status?: number;
}

// 菜单创建请求
export interface CreateMenuRequest {
  parentId?: number;
  name: string;
  path?: string;
  component?: string;
  permission?: string;
  type: number;
  icon?: string;
  orderNo: number;
  status: number;
  show: number;
  keepAlive: number;
  isExt: number;
  extOpenMode: number;
  activeMenu?: string;
  query?: string;
  remark?: string;
}

// 菜单更新请求
export interface UpdateMenuRequest extends Partial<CreateMenuRequest> {
  id: number;
}

// 菜单响应
export interface MenuResponse extends Menu {
  children?: MenuResponse[];
}

// ============= 部门管理 =============

// 部门查询参数
export interface DeptQuery {
  name?: string;
  status?: string;
}

// 部门创建请求
export interface CreateDeptRequest {
  parentId?: number;
  name: string;
  orderNo: number;
  leader?: string;
  phone?: string;
  email?: string;
  status: string;
  remark?: string;
}

// 部门更新请求
export interface UpdateDeptRequest extends Partial<CreateDeptRequest> {
  id: number;
}

// 部门响应
export interface DeptResponse extends Dept {
  children?: DeptResponse[];
}

// ============= 岗位管理 =============

// 岗位查询参数
export interface PostQuery extends PaginationQuery {
  name?: string;
  code?: string;
  status?: string;
}

// 岗位创建请求
export interface CreatePostRequest {
  name: string;
  code: string;
  sort: number;
  status: string;
  remark?: string;
}

// 岗位更新请求
export interface UpdatePostRequest extends Partial<CreatePostRequest> {
  id: number;
}

// 岗位响应
export type PostResponse = Post;

// ============= 字典管理 =============

// 字典类型查询参数
export interface DictTypeQuery extends PaginationQuery {
  name?: string;
  type?: string;
  status?: string;
  beginTime?: string;
  endTime?: string;
}

// 字典类型创建请求
export interface CreateDictTypeRequest {
  name: string;
  type: string;
  status: string;
  remark?: string;
}

// 字典类型更新请求
export interface UpdateDictTypeRequest extends Partial<CreateDictTypeRequest> {
  id: number;
}

// 字典类型响应
export type DictTypeResponse = DictType;

// 字典数据查询参数
export interface DictDataQuery extends PaginationQuery {
  dictType: string;
  label?: string;
  status?: string;
}

// 字典数据创建请求
export interface CreateDictDataRequest {
  dictType: string;
  label: string;
  value: string;
  sort: number;
  status: string;
  cssClass?: string;
  listClass?: string;
  isDefault: boolean;
  remark?: string;
}

// 字典数据更新请求
export interface UpdateDictDataRequest extends Partial<CreateDictDataRequest> {
  id: number;
}

// 字典数据响应
export type DictDataResponse = DictData;

// ============= 通用API类型 =============

// 通用列表响应
export type ListApiResponse<T> = PaginatedApiResponse<T>;

// 通用详情响应
export type DetailApiResponse<T> = ApiResponse<T>;

// 通用操作响应
export type OperationApiResponse = ApiResponse<null>;

// 文件上传响应
export interface FileUploadResponse {
  filename: string;
  originalName: string;
  url: string;
  size: number;
}

// 导出响应
export interface ExportResponse {
  filename: string;
  url: string;
}
