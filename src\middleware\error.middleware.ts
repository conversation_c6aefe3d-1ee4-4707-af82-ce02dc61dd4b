import type { Context, Next } from "hono";
import { ZodError } from "zod";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import { logger } from "@/utils/logger.utils";
import { BaseError, ErrorFactory } from "@/shared/errors";
import { ResponseUtil } from "@/shared/utils/response.util";
import { ErrorCode } from "@/types/common";

/**
 * 全局错误处理中间件
 */
export async function errorMiddleware(c: Context, next: Next) {
  try {
    await next();
  } catch (error: unknown) {
    // 记录错误日志
    const requestId = c.get("requestId") || "unknown";
    logger.error("Application error:", {
      requestId,
      path: c.req.path,
      method: c.req.method,
      userAgent: c.req.header("user-agent"),
      ip:
        c.req.header("x-forwarded-for") ||
        c.req.header("x-real-ip") ||
        "unknown",
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    // 处理自定义业务错误
    if (error instanceof BaseError) {
      return c.json(error.toApiResponse(), error.statusCode);
    }

    // 处理 Zod 验证错误
    if (error instanceof ZodError) {
      const firstError = error.errors[0];
      const message = firstError
        ? `${firstError.path.join(".")}: ${firstError.message}`
        : "请求参数验证失败";

      return ResponseUtil.error(c, ErrorCode.BAD_REQUEST, message, 400, {
        validationErrors: error.errors,
      });
    }

    // 处理 Prisma 错误
    if (error instanceof PrismaClientKnownRequestError) {
      const businessError = ErrorFactory.fromPrismaError(error);
      return c.json(businessError.toApiResponse(), businessError.statusCode);
    }

    // 处理其他已知错误
    if (error instanceof Error) {
      // 检查是否是特定的系统错误
      if (error.message.includes("ECONNREFUSED")) {
        const dbError = ErrorFactory.database("数据库连接失败");
        return c.json(dbError.toApiResponse(), dbError.statusCode);
      }

      if (error.message.includes("Redis")) {
        const redisError = ErrorFactory.redis("Redis连接失败");
        return c.json(redisError.toApiResponse(), redisError.statusCode);
      }

      return ResponseUtil.error(
        c,
        ErrorCode.INTERNAL_ERROR,
        error.message,
        500
      );
    }

    // 处理未知错误
    return ResponseUtil.error(
      c,
      ErrorCode.INTERNAL_ERROR,
      "服务器内部错误",
      500
    );
  }
}

/**
 * 404错误处理中间件
 */
export function notFoundMiddleware(c: Context) {
  return ResponseUtil.error(c, ErrorCode.NOT_FOUND, "请求的资源不存在", 404);
}

/**
 * 异步错误包装器
 * 用于包装异步函数，自动捕获错误
 */
export function asyncHandler(fn: Function) {
  return (c: Context, next?: Next) => {
    return Promise.resolve(fn(c, next)).catch((error) => {
      throw error; // 重新抛出错误，让全局错误处理中间件处理
    });
  };
}
