// src/middleware/login-logger.middleware.ts
import { Context, Next } from "hono";
import { prisma } from "@/db/prisma";
import { redisUtils } from "@/utils/redis.utils";
import { logger } from "@/utils/logger.utils";

/**
 * Login logger middleware - optimized for Bun performance
 * Records login attempts without blocking the response
 */
export async function loginLoggerMiddleware(c: Context, next: Next) {
  // Only activate on login endpoint
  if (c.req.path !== "/api/auth/login") {
    return next();
  }

  // Fast operations before the request (capture data)
  const startTime = Date.now();
  const clientIP =
    c.req.header("x-forwarded-for") || c.req.header("x-real-ip") || "127.0.0.1";
  const userAgent = c.req.header("user-agent") || "";
  let username = "";
  let status = "0"; // Default to success
  let message = "登录成功";

  // Clone the request to extract username
  try {
    const clonedReq = c.req.raw.clone();
    const body = await clonedReq.json();
    username = body?.username || "";
  } catch (error) {
    logger.debug("无法解析登录请求体:", error);
  }

  // Parse browser/OS info synchronously (fast in Bun)
  let browser = "未知";
  let os = "未知";

  try {
    // Simple UA parsing - you could use a more comprehensive library in production
    if (userAgent) {
      if (userAgent.includes("Edge") || userAgent.includes("Edg")) {
        browser = "Edge";
      } else if (
        userAgent.includes("Chrome") &&
        !userAgent.includes("Chromium")
      ) {
        browser = "Chrome";
      } else if (userAgent.includes("Firefox")) {
        browser = "Firefox";
      } else if (
        userAgent.includes("Safari") &&
        !userAgent.includes("Chrome")
      ) {
        browser = "Safari";
      } else if (userAgent.includes("MSIE") || userAgent.includes("Trident")) {
        browser = "IE";
      }

      if (userAgent.includes("Windows")) {
        os = "Windows";
      } else if (userAgent.includes("Mac OS")) {
        os = "Mac OS";
      } else if (userAgent.includes("Linux")) {
        os = "Linux";
      } else if (userAgent.includes("Android")) {
        os = "Android";
      } else if (
        userAgent.includes("iOS") ||
        userAgent.includes("iPhone") ||
        userAgent.includes("iPad")
      ) {
        os = "iOS";
      }
    }
  } catch (error) {
    logger.debug("解析User Agent失败:", error);
  }

  try {
    // Process the request
    await next();

    // Get status from the response
    status = c.res.status < 400 ? "0" : "1";
    message = status === "0" ? "登录成功" : "登录失败";

    // Only proceed if we have a username
    if (!username) {
      return;
    }
  } catch (error) {
    // If an exception occurred, log as a failure
    status = "1";
    message = error instanceof Error ? error.message : "登录失败";

    // Re-throw to ensure the error is handled by error middleware
    throw error;
  } finally {
    // Only log if we have a username
    if (username) {
      // Use Bun.spawn for completely non-blocking logging
      // This ensures the response is never delayed by logging operations
      Bun.spawn({
        cmd: [
          "bun",
          "run",
          "-e",
          `
          // Isolated environment for background processing
          const loginLogId = ${Date.now()};
          
          async function main() {
            try {
              // Import required modules
              const { prisma } = require("${process.cwd()}/src/db/prisma");
              const { redisUtils } = require("${process.cwd()}/src/utils/redis.utils");
              
              // Create login log record first with default values
              const log = await prisma.loginLog.create({
                data: {
                  username: "${username}",
                  ipaddr: "${clientIP}",
                  loginLocation: "未知", // Will be updated asynchronously
                  browser: "${browser}",
                  os: "${os}",
                  status: "${status}",
                  msg: "${message.replace(/"/g, '\\"')}", // Escape quotes
                  loginTime: new Date(),
                  userId: null // Will be updated if login is successful
                }
              });
              
              // If successful login, try to get the user ID
              if ("${status}" === "0") {
                const user = await prisma.user.findUnique({
                  where: { username: "${username}" },
                  select: { id: true }
                });
                
                if (user) {
                  await prisma.loginLog.update({
                    where: { id: log.id },
                    data: { userId: user.id }
                  });
                }
              }
              
              // Handle IP geolocation separately
              await updateLocationInfo(log.id, "${clientIP}");
            } catch (error) {
              console.error("登录日志记录失败:", error);
            }
          }
          
          async function updateLocationInfo(logId, ip) {
            try {
              // Skip for localhost/internal IPs
              if (["127.0.0.1", "::1", "localhost"].includes(ip) || 
                  ip.startsWith("192.168.") || 
                  ip.startsWith("10.") || 
                  (ip.startsWith("172.") && parseInt(ip.split(".")[1]) >= 16 && parseInt(ip.split(".")[1]) <= 31)) {
                await prisma.loginLog.update({
                  where: { id: logId },
                  data: { loginLocation: "内网IP" }
                });
                return;
              }
              
              // Check redis cache first
              const cacheKey = \`geo:ip:\${ip}\`;
              const cachedLocation = await redisUtils.get(cacheKey);
              
              if (cachedLocation) {
                await prisma.loginLog.update({
                  where: { id: logId },
                  data: { loginLocation: cachedLocation }
                });
                return;
              }
              
              // Try to get location from IP-API
              try {
                const response = await fetch(\`http://ip-api.com/json/\${ip}?fields=status,country,regionName,city&lang=zh-CN\`, {
                  headers: { 'User-Agent': 'Hono-Admin/1.0' },
                  timeout: 3000
                });
                
                if (response.ok) {
                  const data = await response.json();
                  if (data.status === "success") {
                    const location = [data.country, data.regionName, data.city]
                      .filter(Boolean)
                      .join(" ")
                      .trim() || "未知位置";
                    
                    // Update log record
                    await prisma.loginLog.update({
                      where: { id: logId },
                      data: { loginLocation: location }
                    });
                    
                    // Cache result for 7 days
                    await redisUtils.set(cacheKey, location, 60*60*24*7);
                    return;
                  }
                }
              } catch (error) {
                console.error("IP地理位置API请求失败:", error);
              }
              
              // Fallback API if primary fails
              try {
                const response = await fetch(\`https://ipapi.co/\${ip}/json/\`);
                if (response.ok) {
                  const data = await response.json();
                  if (!data.error) {
                    const location = [data.country_name, data.region, data.city]
                      .filter(Boolean)
                      .join(" ")
                      .trim() || "未知位置";
                    
                    // Update log record
                    await prisma.loginLog.update({
                      where: { id: logId },
                      data: { loginLocation: location }
                    });
                    
                    // Cache result for 7 days
                    await redisUtils.set(cacheKey, location, 60*60*24*7);
                    return;
                  }
                }
              } catch (error) {
                console.error("备用IP地理位置API请求失败:", error);
              }
              
              // If all APIs fail, mark as unknown
              await prisma.loginLog.update({
                where: { id: logId },
                data: { loginLocation: "未知位置" }
              });
            } catch (error) {
              console.error("更新地理位置信息失败:", error);
            }
          }
          
          // Start the background process
          main().catch(console.error);
        `,
        ],
        stdin: null,
        stdout: "ignore",
        stderr: "inherit", // Log errors to stderr
        env: {}, // Inherit environment variables
        onExit(proc) {
          if (proc.exitCode !== 0) {
            logger.error(`登录日志后台进程异常退出: ${proc.exitCode}`);
          }
        },
      });
    }
  }
}
