{
  "compilerOptions": {
    // 基础配置
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "lib": ["ES2022", "DOM"],
    "types": ["node", "bun"],

    // 严格类型检查
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitOverride": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noFallthroughCasesInSwitch": true,
    "noPropertyAccessFromIndexSignature": true,

    // 输出配置
    "outDir": "dist",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,

    // 模块解析
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "allowImportingTsExtensions": false,
    "verbatimModuleSyntax": false,

    // 路径映射
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/types/*": ["types/*"],
      "@/utils/*": ["utils/*"],
      "@/config/*": ["config/*"],
      "@/middleware/*": ["middleware/*"],
      "@/modules/*": ["modules/*"],
      "@/db/*": ["db/*"],
      "@/routes/*": ["routes/*"],
      "@/shared/*": ["shared/*"]
    },

    // 实验性功能
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,

    // 跳过库检查以提高性能
    "skipLibCheck": true
  },
  "include": [
    "src/**/*",
    "src/**/*.json"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "coverage"
  ],
  "ts-node": {
    "esm": true
  }
}
