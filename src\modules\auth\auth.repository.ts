// src/modules/auth/auth.repository.ts
/**
 * 认证相关数据访问层
 */

import { BaseRepository } from '@/shared/repository/base.repository';
import type { User } from '@/types/entities';

export interface CreateUserInput {
  username: string;
  nickname: string;
  password: string;
  psalt: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  status: string;
  deptId?: number;
  createBy?: string;
  remark?: string;
}

export interface UpdateUserInput {
  nickname?: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  avatar?: string;
  status?: string;
  deptId?: number;
  updateBy?: string;
  remark?: string;
}

/**
 * 用户Repository
 */
export class UserRepository extends BaseRepository<User, CreateUserInput, UpdateUserInput> {
  protected modelName = 'user';

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.model.findUnique({
      where: { username },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.model.findUnique({
      where: { email },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phonenumber: string): Promise<User | null> {
    return this.model.findUnique({
      where: { phonenumber },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 获取用户详细信息（包含角色和权限）
   */
  async findUserWithRolesAndPermissions(userId: number): Promise<User | null> {
    return this.model.findUnique({
      where: { id: userId },
      include: {
        dept: true,
        userRoles: {
          include: {
            role: {
              include: {
                roleMenus: {
                  include: {
                    menu: true,
                  },
                },
              },
            },
          },
        },
        userPosts: {
          include: {
            post: true,
          },
        },
      },
    });
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    // 获取用户角色ID列表
    const userRoles = await this.db.userRole.findMany({
      where: { userId },
      select: { roleId: true },
    });

    const roleIds = userRoles.map((ur) => ur.roleId);

    // 管理员角色判断
    const isAdmin = await this.db.role.findFirst({
      where: {
        id: { in: roleIds },
        key: "admin",
      },
    });

    // 如果是管理员，获取所有菜单权限
    if (isAdmin) {
      const allMenus = await this.db.menu.findMany({
        where: {
          AND: [{ permission: { not: null } }, { permission: { not: "" } }],
        },
        select: { permission: true },
      });

      return allMenus.map((menu) => menu.permission).filter(Boolean) as string[];
    }

    // 非管理员，获取角色对应的菜单权限
    const roleMenus = await this.db.roleMenu.findMany({
      where: { roleId: { in: roleIds } },
      include: { menu: true },
    });

    // 提取权限标识并去重
    const permissions = roleMenus
      .map((rm) => rm.menu.permission)
      .filter(Boolean) as string[];

    return [...new Set(permissions)];
  }

  /**
   * 获取用户路由菜单
   */
  async getUserRouters(userId: number) {
    // 获取用户角色ID列表
    const userRoles = await this.db.userRole.findMany({
      where: { userId },
      select: { roleId: true },
    });

    const roleIds = userRoles.map((ur) => ur.roleId);

    // 管理员角色判断
    const isAdmin = await this.db.role.findFirst({
      where: {
        id: { in: roleIds },
        key: "admin",
      },
    });

    // 查询菜单条件 - 添加type条件，排除按钮类型(type=2)
    const where = isAdmin
      ? {
          status: 1,
          type: { in: [0, 1] }, // 只获取目录(0)和菜单(1)，排除按钮(2)
        }
      : {
          status: 1,
          type: { in: [0, 1] }, // 只获取目录(0)和菜单(1)，排除按钮(2)
          id: {
            in: (
              await this.db.roleMenu.findMany({
                where: { roleId: { in: roleIds } },
                select: { menuId: true },
              })
            ).map((rm) => rm.menuId),
          },
        };

    // 查询所有有效菜单
    const menus = await this.db.menu.findMany({
      where,
      orderBy: { orderNo: "asc" },
    });

    // 构建路由树 - 从顶级菜单开始构建
    return this.buildMenuTree(menus, null);
  }

  /**
   * 构建菜单树
   */
  private buildMenuTree(menus: any[], parentId: number | null) {
    const result: any[] = [];

    menus.forEach((menu) => {
      // 兼容parentId为null或0的情况
      if (
        (parentId === null &&
          (menu.parentId === null || menu.parentId === 0)) ||
        menu.parentId === parentId
      ) {
        const item: any = { ...menu };

        // 递归构建子菜单
        const children = this.buildMenuTree(menus, menu.id);
        if (children.length > 0) {
          item.children = children;
        }

        result.push(item);
      }
    });

    return result;
  }

  /**
   * 更新用户登录信息
   */
  async updateLoginInfo(userId: number, loginIp: string): Promise<void> {
    await this.model.update({
      where: { id: userId },
      data: {
        loginIp,
        loginDate: new Date(),
      },
    });
  }

  /**
   * 检查用户名是否存在
   */
  async isUsernameExists(username: string, excludeId?: number): Promise<boolean> {
    const where: any = { username };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 检查邮箱是否存在
   */
  async isEmailExists(email: string, excludeId?: number): Promise<boolean> {
    const where: any = { email };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 检查手机号是否存在
   */
  async isPhoneExists(phonenumber: string, excludeId?: number): Promise<boolean> {
    const where: any = { phonenumber };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 获取关联查询选项
   */
  protected getIncludeOptions() {
    return {
      include: {
        dept: {
          select: {
            id: true,
            name: true,
          },
        },
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                key: true,
              },
            },
          },
        },
        userPosts: {
          include: {
            post: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    };
  }

  /**
   * 支持软删除
   */
  protected supportsSoftDelete(): boolean {
    return true;
  }

  /**
   * 构建查询条件
   */
  protected buildWhereCondition(query: any): any {
    const where = super.buildWhereCondition(query);

    // 用户名模糊查询
    if (query.username) {
      where.username = {
        contains: query.username,
      };
    }

    // 昵称模糊查询
    if (query.nickname) {
      where.nickname = {
        contains: query.nickname,
      };
    }

    // 部门过滤
    if (query.deptId) {
      where.deptId = parseInt(query.deptId);
    }

    return where;
  }
}
