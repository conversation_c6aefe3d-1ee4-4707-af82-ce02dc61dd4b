// src/shared/utils/datetime.util.ts
/**
 * 日期时间工具类
 */

/**
 * 日期时间工具类
 */
export class DateTimeUtil {
  /**
   * 格式化日期
   */
  static format(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const d = new Date(date);
    
    if (isNaN(d.getTime())) {
      throw new Error('Invalid date');
    }

    const year = d.getFullYear();
    const month = d.getMonth() + 1;
    const day = d.getDate();
    const hour = d.getHours();
    const minute = d.getMinutes();
    const second = d.getSeconds();
    const millisecond = d.getMilliseconds();

    const formatMap: Record<string, string> = {
      'YYYY': year.toString(),
      'YY': year.toString().slice(-2),
      'MM': month.toString().padStart(2, '0'),
      'M': month.toString(),
      'DD': day.toString().padStart(2, '0'),
      'D': day.toString(),
      'HH': hour.toString().padStart(2, '0'),
      'H': hour.toString(),
      'mm': minute.toString().padStart(2, '0'),
      'm': minute.toString(),
      'ss': second.toString().padStart(2, '0'),
      's': second.toString(),
      'SSS': millisecond.toString().padStart(3, '0'),
    };

    let result = format;
    for (const [key, value] of Object.entries(formatMap)) {
      result = result.replace(new RegExp(key, 'g'), value);
    }

    return result;
  }

  /**
   * 解析日期字符串
   */
  static parse(dateStr: string, format?: string): Date {
    if (format) {
      // 简单的格式解析实现
      // 实际项目中建议使用 date-fns 或 moment.js
      return new Date(dateStr);
    }
    return new Date(dateStr);
  }

  /**
   * 获取当前时间戳（秒）
   */
  static now(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   */
  static nowMs(): number {
    return Date.now();
  }

  /**
   * 获取今天开始时间
   */
  static startOfDay(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * 获取今天结束时间
   */
  static endOfDay(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * 获取本周开始时间
   */
  static startOfWeek(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一为一周开始
    d.setDate(diff);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * 获取本周结束时间
   */
  static endOfWeek(date?: Date): Date {
    const d = this.startOfWeek(date);
    d.setDate(d.getDate() + 6);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * 获取本月开始时间
   */
  static startOfMonth(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setDate(1);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * 获取本月结束时间
   */
  static endOfMonth(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setMonth(d.getMonth() + 1, 0);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * 获取本年开始时间
   */
  static startOfYear(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setMonth(0, 1);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * 获取本年结束时间
   */
  static endOfYear(date?: Date): Date {
    const d = date ? new Date(date) : new Date();
    d.setMonth(11, 31);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * 添加时间
   */
  static add(date: Date, amount: number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): Date {
    const d = new Date(date);
    
    switch (unit) {
      case 'year':
        d.setFullYear(d.getFullYear() + amount);
        break;
      case 'month':
        d.setMonth(d.getMonth() + amount);
        break;
      case 'day':
        d.setDate(d.getDate() + amount);
        break;
      case 'hour':
        d.setHours(d.getHours() + amount);
        break;
      case 'minute':
        d.setMinutes(d.getMinutes() + amount);
        break;
      case 'second':
        d.setSeconds(d.getSeconds() + amount);
        break;
    }
    
    return d;
  }

  /**
   * 减去时间
   */
  static subtract(date: Date, amount: number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): Date {
    return this.add(date, -amount, unit);
  }

  /**
   * 计算两个日期之间的差值
   */
  static diff(date1: Date, date2: Date, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' = 'day'): number {
    const diffMs = date1.getTime() - date2.getTime();
    
    switch (unit) {
      case 'year':
        return Math.floor(diffMs / (1000 * 60 * 60 * 24 * 365));
      case 'month':
        return Math.floor(diffMs / (1000 * 60 * 60 * 24 * 30));
      case 'day':
        return Math.floor(diffMs / (1000 * 60 * 60 * 24));
      case 'hour':
        return Math.floor(diffMs / (1000 * 60 * 60));
      case 'minute':
        return Math.floor(diffMs / (1000 * 60));
      case 'second':
        return Math.floor(diffMs / 1000);
      default:
        return diffMs;
    }
  }

  /**
   * 判断是否为同一天
   */
  static isSameDay(date1: Date, date2: Date): boolean {
    return this.format(date1, 'YYYY-MM-DD') === this.format(date2, 'YYYY-MM-DD');
  }

  /**
   * 判断是否为今天
   */
  static isToday(date: Date): boolean {
    return this.isSameDay(date, new Date());
  }

  /**
   * 判断是否为昨天
   */
  static isYesterday(date: Date): boolean {
    const yesterday = this.subtract(new Date(), 1, 'day');
    return this.isSameDay(date, yesterday);
  }

  /**
   * 判断是否为明天
   */
  static isTomorrow(date: Date): boolean {
    const tomorrow = this.add(new Date(), 1, 'day');
    return this.isSameDay(date, tomorrow);
  }

  /**
   * 判断是否为工作日
   */
  static isWeekday(date: Date): boolean {
    const day = date.getDay();
    return day >= 1 && day <= 5;
  }

  /**
   * 判断是否为周末
   */
  static isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6;
  }

  /**
   * 判断是否为闰年
   */
  static isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
  }

  /**
   * 获取月份天数
   */
  static getDaysInMonth(year: number, month: number): number {
    return new Date(year, month, 0).getDate();
  }

  /**
   * 获取相对时间描述
   */
  static getRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return this.format(date, 'YYYY-MM-DD');
    }
  }

  /**
   * 获取时区偏移
   */
  static getTimezoneOffset(): number {
    return new Date().getTimezoneOffset();
  }

  /**
   * 转换为UTC时间
   */
  static toUTC(date: Date): Date {
    return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  }

  /**
   * 从UTC时间转换为本地时间
   */
  static fromUTC(date: Date): Date {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000);
  }

  /**
   * 获取年龄
   */
  static getAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }
}
