// src/modules/auth/auth.controller.ts
import type { Context } from "hono";
import { AuthService } from "./auth.service";
import type { LoginDto } from "./auth.schema";
import { ResponseUtil } from "@/shared/utils/response.util";
import { getCurrentUser } from "@/middleware/auth.middleware";

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // 获取验证码
  getCodeImg = async (c: Context) => {
    const clientIP =
      c.req.header("x-forwarded-for") || c.req.header("x-real-ip") || "unknown";

    const imageBase64 = await this.authService.getCodeImg(clientIP);

    return ResponseUtil.success(c, imageBase64, "获取验证码成功");
  };

  // 登录
  login = async (c: Context) => {
    const loginDto = (await c.req.json()) as LoginDto;
    const clientIP =
      c.req.header("x-forwarded-for") || c.req.header("x-real-ip") || "unknown";

    const token = await this.authService.login(loginDto, clientIP);

    return ResponseUtil.success(c, { token }, "登录成功");
  };

  // 退出登录
  logout = async (c: Context) => {
    const authHeader = c.req.header("Authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return ResponseUtil.error(c, 401, "未授权，请先登录", 401);
    }

    const token = authHeader.split(" ")[1];
    if (!token) {
      return ResponseUtil.error(c, 401, "Token格式错误", 401);
    }

    await this.authService.logout(token);

    return ResponseUtil.success(c, null, "退出登录成功");
  };

  // 获取用户信息（含角色 权限）
  getInfo = async (c: Context) => {
    const user = getCurrentUser(c);
    const userInfo = await this.authService.getUserInfo(user.id);

    return ResponseUtil.success(c, userInfo, "获取用户信息成功");
  };

  // 获取用户可访问的路由菜单
  getRouters = async (c: Context) => {
    const user = getCurrentUser(c);
    const routers = await this.authService.getUserRouters(user.id);

    return ResponseUtil.success(c, routers, "获取路由成功");
  };
}
