// src/modules/system/user/user.repository.ts
/**
 * 用户数据访问层
 */

import { BaseRepository } from '@/shared/repository/base.repository';
import type { User } from '@/types/entities';
import type { CreateUserRequest, UpdateUserRequest, UserQuery } from '@/types/api';

/**
 * 用户Repository
 */
export class UserRepository extends BaseRepository<User, CreateUserRequest, UpdateUserRequest> {
  protected modelName = 'user';

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.model.findUnique({
      where: { username },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.model.findUnique({
      where: { email },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phonenumber: string): Promise<User | null> {
    return this.model.findUnique({
      where: { phonenumber },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 检查用户名是否存在
   */
  async isUsernameExists(username: string, excludeId?: number): Promise<boolean> {
    const where: any = { username };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 检查邮箱是否存在
   */
  async isEmailExists(email: string, excludeId?: number): Promise<boolean> {
    if (!email) return false;
    const where: any = { email };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 检查手机号是否存在
   */
  async isPhoneExists(phonenumber: string, excludeId?: number): Promise<boolean> {
    if (!phonenumber) return false;
    const where: any = { phonenumber };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    return this.exists(where);
  }

  /**
   * 根据部门ID查找用户
   */
  async findByDeptId(deptId: number): Promise<User[]> {
    return this.model.findMany({
      where: { deptId },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 根据角色ID查找用户
   */
  async findByRoleId(roleId: number): Promise<User[]> {
    return this.model.findMany({
      where: {
        userRoles: {
          some: { roleId },
        },
      },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 批量分配角色
   */
  async assignRoles(userId: number, roleIds: number[]): Promise<void> {
    await this.transaction(async (tx) => {
      // 删除现有角色
      await tx.userRole.deleteMany({
        where: { userId },
      });

      // 分配新角色
      if (roleIds.length > 0) {
        await tx.userRole.createMany({
          data: roleIds.map(roleId => ({ userId, roleId })),
        });
      }
    });
  }

  /**
   * 批量分配岗位
   */
  async assignPosts(userId: number, postIds: number[]): Promise<void> {
    await this.transaction(async (tx) => {
      // 删除现有岗位
      await tx.userPost.deleteMany({
        where: { userId },
      });

      // 分配新岗位
      if (postIds.length > 0) {
        await tx.userPost.createMany({
          data: postIds.map(postId => ({ userId, postId })),
        });
      }
    });
  }

  /**
   * 重置密码
   */
  async resetPassword(userId: number, hashedPassword: string, salt: string): Promise<void> {
    await this.model.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        psalt: salt,
      },
    });
  }

  /**
   * 更新头像
   */
  async updateAvatar(userId: number, avatar: string): Promise<void> {
    await this.model.update({
      where: { id: userId },
      data: { avatar },
    });
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    disabled: number;
    todayRegistered: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [total, active, disabled, todayRegistered] = await Promise.all([
      this.count({ delFlag: '0' }),
      this.count({ delFlag: '0', status: '0' }),
      this.count({ delFlag: '0', status: '1' }),
      this.count({
        delFlag: '0',
        createdAt: { gte: today },
      }),
    ]);

    return {
      total,
      active,
      disabled,
      todayRegistered,
    };
  }

  /**
   * 获取关联查询选项
   */
  protected getIncludeOptions() {
    return {
      include: {
        dept: {
          select: {
            id: true,
            name: true,
          },
        },
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                key: true,
              },
            },
          },
        },
        userPosts: {
          include: {
            post: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    };
  }

  /**
   * 支持软删除
   */
  protected supportsSoftDelete(): boolean {
    return true;
  }

  /**
   * 构建查询条件
   */
  protected buildWhereCondition(query: UserQuery): any {
    const where = super.buildWhereCondition(query);

    // 用户名模糊查询
    if (query.username) {
      where.username = {
        contains: query.username,
      };
    }

    // 昵称模糊查询
    if (query.nickname) {
      where.nickname = {
        contains: query.nickname,
      };
    }

    // 部门过滤
    if (query.deptId) {
      where.deptId = parseInt(query.deptId.toString());
    }

    return where;
  }
}
