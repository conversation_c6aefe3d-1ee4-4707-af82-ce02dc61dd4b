// src/shared/middleware/base.middleware.ts
/**
 * 基础中间件工具类
 */

import type { Context, Next } from 'hono';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger.utils';

/**
 * 请求ID中间件
 * 为每个请求生成唯一ID，用于日志追踪
 */
export function requestIdMiddleware(c: Context, next: Next) {
  const requestId = c.req.header('x-request-id') || uuidv4();
  c.set('requestId', requestId);
  c.header('x-request-id', requestId);
  return next();
}

/**
 * 请求时间中间件
 * 记录请求开始时间，用于性能监控
 */
export function requestTimeMiddleware(c: Context, next: Next) {
  c.set('startTime', Date.now());
  return next();
}

/**
 * 性能监控中间件
 * 记录请求处理时间
 */
export function performanceMiddleware(c: Context, next: Next) {
  return next().finally(() => {
    const startTime = c.get('startTime');
    if (startTime) {
      const duration = Date.now() - startTime;
      const requestId = c.get('requestId');
      
      // 记录慢请求
      if (duration > 1000) {
        logger.warn('Slow request detected:', {
          requestId,
          path: c.req.path,
          method: c.req.method,
          duration: `${duration}ms`,
        });
      }
      
      // 添加响应头
      c.header('x-response-time', `${duration}ms`);
    }
  });
}

/**
 * CORS中间件工厂
 */
export function createCorsMiddleware(options: {
  origin?: string | string[];
  credentials?: boolean;
  allowMethods?: string[];
  allowHeaders?: string[];
  exposeHeaders?: string[];
  maxAge?: number;
} = {}) {
  const {
    origin = '*',
    credentials = true,
    allowMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowHeaders = ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposeHeaders = ['X-Request-ID', 'X-Response-Time'],
    maxAge = 86400,
  } = options;

  return (c: Context, next: Next) => {
    const requestOrigin = c.req.header('origin');
    
    // 设置 CORS 头
    if (Array.isArray(origin)) {
      if (origin.includes(requestOrigin || '')) {
        c.header('Access-Control-Allow-Origin', requestOrigin || '');
      }
    } else if (origin === '*' || origin === requestOrigin) {
      c.header('Access-Control-Allow-Origin', origin);
    }
    
    if (credentials) {
      c.header('Access-Control-Allow-Credentials', 'true');
    }
    
    c.header('Access-Control-Allow-Methods', allowMethods.join(', '));
    c.header('Access-Control-Allow-Headers', allowHeaders.join(', '));
    c.header('Access-Control-Expose-Headers', exposeHeaders.join(', '));
    c.header('Access-Control-Max-Age', maxAge.toString());
    
    // 处理预检请求
    if (c.req.method === 'OPTIONS') {
      return c.text('', 204);
    }
    
    return next();
  };
}

/**
 * 安全头中间件
 */
export function securityHeadersMiddleware(c: Context, next: Next) {
  return next().then(() => {
    // 设置安全相关的响应头
    c.header('X-Content-Type-Options', 'nosniff');
    c.header('X-Frame-Options', 'DENY');
    c.header('X-XSS-Protection', '1; mode=block');
    c.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    c.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    
    // 在生产环境中启用 HSTS
    if (process.env.NODE_ENV === 'production') {
      c.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }
  });
}

/**
 * 请求大小限制中间件工厂
 */
export function createBodySizeLimitMiddleware(maxSize: number = 10 * 1024 * 1024) { // 默认10MB
  return async (c: Context, next: Next) => {
    const contentLength = c.req.header('content-length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      return c.json(
        {
          code: 413,
          message: `请求体大小超过限制 (${maxSize} bytes)`,
        },
        413
      );
    }
    
    return next();
  };
}

/**
 * 速率限制中间件工厂
 */
export function createRateLimitMiddleware(options: {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (c: Context) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) {
  const {
    windowMs,
    maxRequests,
    keyGenerator = (c: Context) => c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
  } = options;

  const requests = new Map<string, { count: number; resetTime: number }>();

  return async (c: Context, next: Next) => {
    const key = keyGenerator(c);
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期的记录
    for (const [k, v] of requests.entries()) {
      if (v.resetTime < now) {
        requests.delete(k);
      }
    }

    // 获取当前窗口的请求记录
    let record = requests.get(key);
    if (!record || record.resetTime < now) {
      record = { count: 0, resetTime: now + windowMs };
      requests.set(key, record);
    }

    // 检查是否超过限制
    if (record.count >= maxRequests) {
      return c.json(
        {
          code: 429,
          message: '请求过于频繁，请稍后再试',
          retryAfter: Math.ceil((record.resetTime - now) / 1000),
        },
        429
      );
    }

    // 执行请求
    await next();

    // 根据配置决定是否计数
    const shouldCount = 
      (!skipSuccessfulRequests || c.res.status >= 400) &&
      (!skipFailedRequests || c.res.status < 400);

    if (shouldCount) {
      record.count++;
    }
  };
}

/**
 * 健康检查中间件
 */
export function healthCheckMiddleware(c: Context, next: Next) {
  if (c.req.path === '/health' || c.req.path === '/ping') {
    return c.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    });
  }
  return next();
}
