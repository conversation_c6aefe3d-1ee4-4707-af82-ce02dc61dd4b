import type { Context, Next } from "hono";
import { PermissionError } from "@/shared/errors";
import type { RequestUser } from "@/types/common";
import { redisUtils } from "@/utils/redis.utils";
import { logger } from "@/utils/logger.utils";

/**
 * 权限检查中间件工厂
 * @param permission 需要的权限标识
 * @returns 中间件函数
 */
export function requirePermission(permission: string) {
  return async (c: Context, next: Next) => {
    const user: RequestUser = c.get("user");

    if (!user) {
      throw new PermissionError("未授权，请先登录");
    }

    // 检查是否为超级管理员
    if (user.roles.includes("admin")) {
      return next();
    }

    // 检查用户是否有所需权限
    if (!user.permissions.includes(permission)) {
      throw new PermissionError(`权限不足，需要权限: ${permission}`);
    }

    await next();
  };
}

/**
 * 角色检查中间件工厂
 * @param roles 需要的角色列表
 * @returns 中间件函数
 */
export function requireRole(...roles: string[]) {
  return async (c: Context, next: Next) => {
    const user: RequestUser = c.get("user");

    if (!user) {
      throw new PermissionError("未授权，请先登录");
    }

    // 检查用户是否有所需角色
    const hasRole = roles.some((role) => user.roles.includes(role));

    if (!hasRole) {
      throw new PermissionError(`权限不足，需要角色: ${roles.join(" 或 ")}`);
    }

    await next();
  };
}

/**
 * 多权限检查中间件工厂（需要所有权限）
 * @param permissions 需要的权限列表
 * @returns 中间件函数
 */
export function requireAllPermissions(...permissions: string[]) {
  return async (c: Context, next: Next) => {
    const user: RequestUser = c.get("user");

    if (!user) {
      throw new PermissionError("未授权，请先登录");
    }

    // 检查是否为超级管理员
    if (user.roles.includes("admin")) {
      return next();
    }

    // 检查用户是否有所有所需权限
    const hasAllPermissions = permissions.every((permission) =>
      user.permissions.includes(permission)
    );

    if (!hasAllPermissions) {
      const missingPermissions = permissions.filter(
        (permission) => !user.permissions.includes(permission)
      );
      throw new PermissionError(
        `权限不足，缺少权限: ${missingPermissions.join(", ")}`
      );
    }

    await next();
  };
}

/**
 * 多权限检查中间件工厂（需要任一权限）
 * @param permissions 需要的权限列表
 * @returns 中间件函数
 */
export function requireAnyPermission(...permissions: string[]) {
  return async (c: Context, next: Next) => {
    const user: RequestUser = c.get("user");

    if (!user) {
      throw new PermissionError("未授权，请先登录");
    }

    // 检查是否为超级管理员
    if (user.roles.includes("admin")) {
      return next();
    }

    // 检查用户是否有任一所需权限
    const hasAnyPermission = permissions.some((permission) =>
      user.permissions.includes(permission)
    );

    if (!hasAnyPermission) {
      throw new PermissionError(
        `权限不足，需要以下任一权限: ${permissions.join(", ")}`
      );
    }

    await next();
  };
}

/**
 * 资源所有者检查中间件工厂
 * @param getResourceOwnerId 获取资源所有者ID的函数
 * @returns 中间件函数
 */
export function requireResourceOwner(
  getResourceOwnerId: (c: Context) => Promise<number> | number
) {
  return async (c: Context, next: Next) => {
    const user: RequestUser = c.get("user");

    if (!user) {
      throw new PermissionError("未授权，请先登录");
    }

    // 检查是否为超级管理员
    if (user.roles.includes("admin")) {
      return next();
    }

    // 获取资源所有者ID
    const resourceOwnerId = await getResourceOwnerId(c);

    // 检查是否为资源所有者
    if (user.id !== resourceOwnerId) {
      throw new PermissionError("权限不足，只能操作自己的资源");
    }

    await next();
  };
}

/**
 * 清除用户权限缓存
 * @param userId 用户 ID
 */
export async function clearPermissionCache(userId: number): Promise<void> {
  const cacheKey = `user:${userId}:permissions`;
  await redisUtils.del(cacheKey);
  logger.info(`已清除用户 ${userId} 的权限缓存，Key=${cacheKey}`);
}
