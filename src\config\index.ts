// src/config/index.ts
import {
  env,
  checkRequiredEnv,
  getDatabaseConfig,
  getRedisConfig,
  getAuthConfig,
  getLoggerConfig,
  getUploadConfig,
  getCorsConfig,
  getRateLimitConfig,
  isDevelopment,
  isProduction,
  isTest,
} from "./env.validation";

// 检查必需的环境变量
checkRequiredEnv();

// 统一配置对象
export const config = {
  // 应用配置
  app: {
    name: "Hono Admin",
    env: env.NODE_ENV,
    host: env.HOST,
    port: env.PORT,
    version: env.APP_VERSION,
    baseUrl: env.BASE_URL || `http://${env.HOST}:${env.PORT}`,
    apiPrefix: env.API_PREFIX,
    timezone: env.TIMEZONE,
    isDevelopment,
    isProduction,
    isTest,
  },

  // 数据库配置
  db: getDatabaseConfig(),

  // Redis配置
  redis: getRedisConfig(),

  // 认证配置
  auth: getAuthConfig(),

  // 日志配置
  logger: getLoggerConfig(),

  // 文件上传配置
  upload: getUploadConfig(),

  // CORS配置
  cors: getCorsConfig(),

  // 速率限制配置
  rateLimit: getRateLimitConfig(),

  // 安全配置
  security: {
    bodySizeLimit: env.BODY_SIZE_LIMIT,
  },

  // AI配置
  ai: {
    openaiApiKey: env.OPENAI_API_KEY,
    anthropicApiKey: env.ANTHROPIC_API_KEY,
    googleApiKey: env.GOOGLE_API_KEY,
    defaultModel: env.AI_DEFAULT_MODEL,
    maxTokens: env.AI_MAX_TOKENS,
    temperature: env.AI_TEMPERATURE,
  },

  // 邮件配置
  mail: {
    host: env.MAIL_HOST,
    port: env.MAIL_PORT,
    user: env.MAIL_USER,
    pass: env.MAIL_PASS,
    from: env.MAIL_FROM,
  },

  // 监控配置
  monitoring: {
    healthCheckEnabled: env.HEALTH_CHECK_ENABLED,
    metricsEnabled: env.METRICS_ENABLED,
    performanceMonitoring: env.PERFORMANCE_MONITORING,
  },
};

// 导出环境变量
export { env };

// 导出类型
export type Config = typeof config;
