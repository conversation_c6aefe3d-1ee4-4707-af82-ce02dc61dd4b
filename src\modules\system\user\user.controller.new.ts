// src/modules/system/user/user.controller.new.ts
/**
 * 用户控制器
 */

import type { Context } from 'hono';
import { BaseController } from '@/shared/controller/base.controller';
import { UserService } from './user.service';
import type { User } from '@/types/entities';
import type { CreateUserRequest, UpdateUserRequest } from '@/types/api';
import { ResponseUtil } from '@/shared/utils/response.util';

/**
 * 用户控制器
 */
export class UserController extends BaseController<User, CreateUserRequest, UpdateUserRequest> {
  protected service: UserService;

  constructor() {
    super();
    this.service = new UserService();
  }

  /**
   * 重置密码
   */
  resetPassword = async (c: Context) => {
    const id = this.validateId(c);
    const { password } = await this.getRequestBody<{ password?: string }>(c);
    
    await this.service.resetPassword(id, password);
    
    return ResponseUtil.success(c, null, '密码重置成功');
  };

  /**
   * 修改密码
   */
  changePassword = async (c: Context) => {
    const user = this.getCurrentUser(c);
    const { oldPassword, newPassword } = await this.getRequestBody<{
      oldPassword: string;
      newPassword: string;
    }>(c);
    
    await this.service.changePassword(user.id, oldPassword, newPassword);
    
    return ResponseUtil.success(c, null, '密码修改成功');
  };

  /**
   * 更新头像
   */
  updateAvatar = async (c: Context) => {
    const user = this.getCurrentUser(c);
    const { avatar } = await this.getRequestBody<{ avatar: string }>(c);
    
    await this.service.updateAvatar(user.id, avatar);
    
    return ResponseUtil.success(c, null, '头像更新成功');
  };

  /**
   * 获取个人信息
   */
  getProfile = async (c: Context) => {
    const user = this.getCurrentUser(c);
    const profile = await this.service.findById(user.id);
    
    return ResponseUtil.success(c, profile, '获取个人信息成功');
  };

  /**
   * 更新个人信息
   */
  updateProfile = async (c: Context) => {
    const user = this.getCurrentUser(c);
    const data = await this.getRequestBody<UpdateUserRequest>(c);
    
    // 只允许更新部分字段
    const allowedFields = ['nickname', 'email', 'phonenumber', 'sex'];
    const updateData = Object.keys(data)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = data[key];
        return obj;
      }, {} as any);
    
    const result = await this.service.update(user.id, updateData);
    
    return ResponseUtil.success(c, result, '个人信息更新成功');
  };

  /**
   * 根据用户名查找用户
   */
  findByUsername = async (c: Context) => {
    const { username } = this.getQueryParams(c);
    
    if (!username) {
      return ResponseUtil.error(c, 400, '用户名不能为空');
    }
    
    const user = await this.service.findByUsername(username);
    
    return ResponseUtil.success(c, user, '查询成功');
  };

  /**
   * 获取用户统计信息
   */
  getStats = async (c: Context) => {
    const stats = await this.service.getStats();
    
    return ResponseUtil.success(c, stats, '获取统计信息成功');
  };

  /**
   * 导出用户数据
   */
  export = async (c: Context) => {
    const query = this.getQueryParams(c);
    const data = await this.service.exportUsers(query);
    
    // 这里可以根据需要生成Excel文件
    // 暂时返回JSON数据
    return ResponseUtil.success(c, data, '导出成功');
  };

  /**
   * 批量导入用户
   */
  import = async (c: Context) => {
    const { users } = await this.getRequestBody<{ users: CreateUserRequest[] }>(c);
    
    const results = await this.service.batchOperation(
      users,
      async (userData) => this.service.create(userData)
    );
    
    return ResponseUtil.success(c, {
      total: users.length,
      success: results.length,
      failed: users.length - results.length,
    }, '批量导入完成');
  };

  /**
   * 启用/禁用用户
   */
  toggleStatus = async (c: Context) => {
    const id = this.validateId(c);
    const { status } = await this.getRequestBody<{ status: string }>(c);
    
    const user = this.getCurrentUser(c);
    const result = await this.service.update(id, {
      status,
      updateBy: user.username,
    });
    
    const statusText = status === '0' ? '启用' : '禁用';
    return ResponseUtil.success(c, result, `用户${statusText}成功`);
  };

  /**
   * 分配角色
   */
  assignRoles = async (c: Context) => {
    const id = this.validateId(c);
    const { roleIds } = await this.getRequestBody<{ roleIds: number[] }>(c);
    
    const user = this.getCurrentUser(c);
    const result = await this.service.update(id, {
      roleIds,
      updateBy: user.username,
    });
    
    return ResponseUtil.success(c, result, '角色分配成功');
  };

  /**
   * 分配岗位
   */
  assignPosts = async (c: Context) => {
    const id = this.validateId(c);
    const { postIds } = await this.getRequestBody<{ postIds: number[] }>(c);
    
    const user = this.getCurrentUser(c);
    const result = await this.service.update(id, {
      postIds,
      updateBy: user.username,
    });
    
    return ResponseUtil.success(c, result, '岗位分配成功');
  };

  /**
   * 获取选项字段
   */
  protected getOptionFields(): any {
    return {
      id: true,
      username: true,
      nickname: true,
    };
  }

  /**
   * 准备复制数据
   */
  protected prepareCopyData(original: User): CreateUserRequest {
    const { id, createdAt, updatedAt, createBy, updateBy, password, psalt, ...copyData } = original as any;
    return {
      ...copyData,
      username: `${copyData.username}_copy_${Date.now()}`,
      nickname: `${copyData.nickname}_副本`,
    } as CreateUserRequest;
  }
}
