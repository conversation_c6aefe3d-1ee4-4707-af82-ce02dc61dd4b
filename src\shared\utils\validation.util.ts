// src/shared/utils/validation.util.ts
/**
 * 验证工具类
 */

import { z } from 'zod';

/**
 * 验证工具类
 */
export class ValidationUtil {
  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证手机号格式（中国大陆）
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证身份证号格式
   */
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCard);
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // 长度检查
    if (password.length < 8) {
      feedback.push('密码长度至少8位');
    } else if (password.length >= 12) {
      score += 2;
    } else {
      score += 1;
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('密码应包含小写字母');
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('密码应包含大写字母');
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('密码应包含数字');
    }

    // 包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('密码应包含特殊字符');
    }

    // 不包含常见弱密码
    const weakPasswords = ['123456', 'password', 'admin', 'qwerty'];
    if (weakPasswords.some(weak => password.toLowerCase().includes(weak))) {
      score -= 2;
      feedback.push('密码不能包含常见弱密码');
    }

    return {
      isValid: score >= 4 && feedback.length === 0,
      score: Math.max(0, Math.min(5, score)),
      feedback,
    };
  }

  /**
   * 验证URL格式
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证IP地址格式
   */
  static isValidIP(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * 验证JSON格式
   */
  static isValidJSON(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证文件类型
   */
  static isValidFileType(filename: string, allowedTypes: string[]): boolean {
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? allowedTypes.includes(ext) : false;
  }

  /**
   * 验证文件大小
   */
  static isValidFileSize(size: number, maxSize: number): boolean {
    return size <= maxSize;
  }

  /**
   * 清理和验证用户输入
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // 移除潜在的HTML标签
      .replace(/javascript:/gi, '') // 移除JavaScript协议
      .replace(/on\w+=/gi, ''); // 移除事件处理器
  }

  /**
   * 验证数字范围
   */
  static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * 验证字符串长度
   */
  static isValidLength(str: string, min: number, max: number): boolean {
    return str.length >= min && str.length <= max;
  }

  /**
   * 验证日期格式
   */
  static isValidDate(dateStr: string): boolean {
    const date = new Date(dateStr);
    return !isNaN(date.getTime());
  }

  /**
   * 验证日期范围
   */
  static isDateInRange(dateStr: string, startDate: string, endDate: string): boolean {
    const date = new Date(dateStr);
    const start = new Date(startDate);
    const end = new Date(endDate);
    return date >= start && date <= end;
  }

  /**
   * 使用Zod schema验证数据
   */
  static validateWithSchema<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    errors?: string[];
  } {
    try {
      const result = schema.parse(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        );
        return { success: false, errors };
      }
      return { success: false, errors: ['验证失败'] };
    }
  }

  /**
   * 批量验证
   */
  static validateBatch(validations: Array<{
    name: string;
    value: any;
    validator: (value: any) => boolean;
    message: string;
  }>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    for (const validation of validations) {
      if (!validation.validator(validation.value)) {
        errors.push(`${validation.name}: ${validation.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证对象属性
   */
  static validateObject(obj: any, rules: Record<string, {
    required?: boolean;
    type?: string;
    validator?: (value: any) => boolean;
    message?: string;
  }>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    for (const [key, rule] of Object.entries(rules)) {
      const value = obj[key];

      // 检查必填项
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${key}是必填项`);
        continue;
      }

      // 如果值为空且不是必填项，跳过后续验证
      if (value === undefined || value === null || value === '') {
        continue;
      }

      // 检查类型
      if (rule.type && typeof value !== rule.type) {
        errors.push(`${key}类型错误，期望${rule.type}，实际${typeof value}`);
        continue;
      }

      // 自定义验证器
      if (rule.validator && !rule.validator(value)) {
        errors.push(rule.message || `${key}验证失败`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
