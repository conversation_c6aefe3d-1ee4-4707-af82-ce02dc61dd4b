// src/shared/repository/base.repository.ts
/**
 * 基础Repository类
 */

import { PrismaClient } from '@prisma/client';
import { prisma } from '@/db/prisma';
import type { PaginationQuery, PaginationResult } from '@/types/common';
import { ResponseUtil } from '@/shared/utils/response.util';

/**
 * 基础Repository接口
 */
export interface IBaseRepository<T, CreateInput, UpdateInput> {
  findById(id: number): Promise<T | null>;
  findMany(query?: any): Promise<T[]>;
  findWithPagination(query: PaginationQuery & any): Promise<PaginationResult<T>>;
  create(data: CreateInput): Promise<T>;
  update(id: number, data: UpdateInput): Promise<T>;
  delete(id: number): Promise<void>;
  softDelete?(id: number): Promise<T>;
  count(where?: any): Promise<number>;
}

/**
 * 基础Repository抽象类
 */
export abstract class BaseRepository<T, CreateInput, UpdateInput> 
  implements IBaseRepository<T, CreateInput, UpdateInput> {
  
  protected db: PrismaClient;
  protected abstract modelName: string;

  constructor() {
    this.db = prisma;
  }

  /**
   * 获取Prisma模型
   */
  protected get model() {
    return (this.db as any)[this.modelName];
  }

  /**
   * 根据ID查找记录
   */
  async findById(id: number): Promise<T | null> {
    return this.model.findUnique({
      where: { id },
    });
  }

  /**
   * 查找多条记录
   */
  async findMany(query: any = {}): Promise<T[]> {
    return this.model.findMany(query);
  }

  /**
   * 分页查询
   */
  async findWithPagination(query: PaginationQuery & any): Promise<PaginationResult<T>> {
    const { pageNum, pageSize, skip, take } = ResponseUtil.validatePaginationParams(
      query.pageNum,
      query.pageSize
    );

    // 构建查询条件
    const where = this.buildWhereCondition(query);
    const orderBy = ResponseUtil.handleOrderBy(query.orderBy, query.orderDirection);

    // 并行执行查询和计数
    const [list, total] = await Promise.all([
      this.model.findMany({
        where,
        orderBy,
        skip,
        take,
        ...this.getIncludeOptions(),
      }),
      this.model.count({ where }),
    ]);

    return ResponseUtil.createPaginationResult(list, total, pageNum, pageSize);
  }

  /**
   * 创建记录
   */
  async create(data: CreateInput): Promise<T> {
    return this.model.create({
      data,
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 更新记录
   */
  async update(id: number, data: UpdateInput): Promise<T> {
    return this.model.update({
      where: { id },
      data,
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 删除记录
   */
  async delete(id: number): Promise<void> {
    await this.model.delete({
      where: { id },
    });
  }

  /**
   * 软删除记录
   */
  async softDelete(id: number): Promise<T> {
    return this.model.update({
      where: { id },
      data: { delFlag: '1' },
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 计数
   */
  async count(where: any = {}): Promise<number> {
    return this.model.count({ where });
  }

  /**
   * 批量创建
   */
  async createMany(data: CreateInput[]): Promise<{ count: number }> {
    return this.model.createMany({
      data,
      skipDuplicates: true,
    });
  }

  /**
   * 批量更新
   */
  async updateMany(where: any, data: Partial<UpdateInput>): Promise<{ count: number }> {
    return this.model.updateMany({
      where,
      data,
    });
  }

  /**
   * 批量删除
   */
  async deleteMany(where: any): Promise<{ count: number }> {
    return this.model.deleteMany({
      where,
    });
  }

  /**
   * 检查记录是否存在
   */
  async exists(where: any): Promise<boolean> {
    const count = await this.model.count({ where });
    return count > 0;
  }

  /**
   * 查找第一条记录
   */
  async findFirst(where: any): Promise<T | null> {
    return this.model.findFirst({
      where,
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 查找唯一记录
   */
  async findUnique(where: any): Promise<T | null> {
    return this.model.findUnique({
      where,
      ...this.getIncludeOptions(),
    });
  }

  /**
   * 构建查询条件（子类可重写）
   */
  protected buildWhereCondition(query: any): any {
    const where: any = {};

    // 处理软删除
    if (this.supportsSoftDelete()) {
      where.delFlag = '0';
    }

    // 处理状态过滤
    if (query.status !== undefined) {
      where.status = query.status;
    }

    // 处理时间范围
    if (query.beginTime || query.endTime) {
      where.createdAt = {};
      if (query.beginTime) {
        where.createdAt.gte = new Date(query.beginTime);
      }
      if (query.endTime) {
        where.createdAt.lte = new Date(query.endTime);
      }
    }

    return where;
  }

  /**
   * 获取关联查询选项（子类可重写）
   */
  protected getIncludeOptions(): any {
    return {};
  }

  /**
   * 是否支持软删除（子类可重写）
   */
  protected supportsSoftDelete(): boolean {
    return false;
  }

  /**
   * 事务执行
   */
  async transaction<R>(fn: (tx: PrismaClient) => Promise<R>): Promise<R> {
    return this.db.$transaction(fn);
  }

  /**
   * 原始查询
   */
  async rawQuery(sql: string, params?: any[]): Promise<any> {
    return this.db.$queryRawUnsafe(sql, ...(params || []));
  }

  /**
   * 原始执行
   */
  async rawExecute(sql: string, params?: any[]): Promise<any> {
    return this.db.$executeRawUnsafe(sql, ...(params || []));
  }
}
