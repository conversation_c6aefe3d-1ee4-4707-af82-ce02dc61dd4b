// src/types/entities.ts
/**
 * 数据库实体类型定义
 */

import type { 
  BaseEntity, 
  SoftDeleteEntity, 
  StatusEntity, 
  SortableEntity, 
  TreeEntity 
} from './common';

// 用户实体
export interface User extends SoftDeleteEntity, StatusEntity {
  deptId?: number;
  username: string;
  nickname: string;
  userType?: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  avatar?: string;
  password: string;
  psalt: string;
  loginIp?: string;
  loginDate?: Date;
  
  // 关联数据
  dept?: Dept;
  userRoles?: UserRole[];
  userPosts?: UserPost[];
}

// 角色实体
export interface Role extends SoftDeleteEntity, StatusEntity, SortableEntity {
  name: string;
  key: string;
  dataScope?: string;
  menuCheckStrictly?: boolean;
  deptCheckStrictly?: boolean;
  
  // 关联数据
  userRoles?: UserRole[];
  roleMenus?: RoleMenu[];
  roleDepts?: RoleDept[];
}

// 菜单实体
export interface Menu extends BaseEntity, SortableEntity, TreeEntity {
  path?: string;
  name: string;
  permission?: string;
  type: number;
  icon?: string;
  component?: string;
  keepAlive: number;
  show: number;
  status: number;
  isExt: number;
  extOpenMode: number;
  activeMenu?: string;
  query?: string;
  
  // 关联数据
  children?: Menu[];
  roleMenus?: RoleMenu[];
}

// 部门实体
export interface Dept extends SoftDeleteEntity, StatusEntity, SortableEntity, TreeEntity {
  ancestors: string;
  name: string;
  leader?: string;
  phone?: string;
  email?: string;
  
  // 关联数据
  children?: Dept[];
  users?: User[];
  roleDepts?: RoleDept[];
}

// 岗位实体
export interface Post extends BaseEntity, SortableEntity {
  code: string;
  name: string;
  status: string;
  
  // 关联数据
  userPosts?: UserPost[];
}

// 字典类型实体
export interface DictType extends BaseEntity, StatusEntity {
  name: string;
  type: string;
  
  // 关联数据
  dictData?: DictData[];
}

// 字典数据实体
export interface DictData extends BaseEntity, StatusEntity, SortableEntity {
  dictType: string;
  label: string;
  value: string;
  cssClass?: string;
  listClass?: string;
  isDefault: boolean;
}

// 用户角色关联
export interface UserRole {
  userId: number;
  roleId: number;
  user?: User;
  role?: Role;
}

// 用户岗位关联
export interface UserPost {
  userId: number;
  postId: number;
  user?: User;
  post?: Post;
}

// 角色菜单关联
export interface RoleMenu {
  roleId: number;
  menuId: number;
  role?: Role;
  menu?: Menu;
}

// 角色部门关联
export interface RoleDept {
  roleId: number;
  deptId: number;
  role?: Role;
  dept?: Dept;
}

// 操作日志实体
export interface OperLog extends BaseEntity {
  title: string;
  businessType: number;
  method: string;
  requestMethod: string;
  operatorType: number;
  operName: string;
  deptName?: string;
  operUrl: string;
  operIp: string;
  operLocation?: string;
  operParam?: string;
  jsonResult?: string;
  status: number;
  errorMsg?: string;
  operTime: Date;
  costTime: number;
}

// 登录日志实体
export interface LoginLog extends BaseEntity {
  userName: string;
  ipaddr: string;
  loginLocation?: string;
  browser?: string;
  os?: string;
  status: string;
  msg?: string;
  loginTime: Date;
}

// 文件上传实体
export interface Upload extends BaseEntity {
  userId?: number;
  filename: string;
  filePath: string;
  mimetype: string;
  size: number;
  
  // 关联数据
  user?: User;
}

// AI会话实体
export interface Session extends BaseEntity {
  userId: number;
  title: string;
  model: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  totalTokens: number;
  
  // 关联数据
  user?: User;
  messages?: Message[];
}

// AI消息实体
export interface Message extends BaseEntity {
  sessionId: number;
  content: string;
  role: string;
  fileUrl?: string;
  mimeType?: string;
  tokens?: number;
  
  // 关联数据
  session?: Session;
}

// 配置实体
export interface Config extends BaseEntity {
  name: string;
  key: string;
  value: string;
  type: string;
  
  // 关联数据
  configType?: string;
}
