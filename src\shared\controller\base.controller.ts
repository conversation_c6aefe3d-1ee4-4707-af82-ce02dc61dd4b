// src/shared/controller/base.controller.ts
/**
 * 基础Controller类
 */

import type { Context } from 'hono';
import type { IBaseService } from '@/shared/service/base.service';
import type { PaginationQuery } from '@/types/common';
import { ResponseUtil } from '@/shared/utils/response.util';
import { getCurrentUser } from '@/middleware/auth.middleware';

/**
 * 基础Controller抽象类
 */
export abstract class BaseController<T, CreateInput, UpdateInput> {
  protected abstract service: IBaseService<T, CreateInput, UpdateInput>;

  /**
   * 获取列表（分页）
   */
  list = async (c: Context) => {
    const query = c.req.query() as PaginationQuery & any;
    const result = await this.service.findWithPagination(query);
    return ResponseUtil.successWithPagination(c, result, '获取列表成功');
  };

  /**
   * 获取所有记录（不分页）
   */
  listAll = async (c: Context) => {
    const query = c.req.query();
    const result = await this.service.findMany(query);
    return ResponseUtil.success(c, result, '获取列表成功');
  };

  /**
   * 获取详情
   */
  getInfo = async (c: Context) => {
    const id = parseInt(c.req.param('id'));
    const result = await this.service.findById(id);
    return ResponseUtil.success(c, result, '获取详情成功');
  };

  /**
   * 创建记录
   */
  create = async (c: Context) => {
    const data = await c.req.json() as CreateInput;
    
    // 添加创建者信息
    const user = getCurrentUser(c);
    const createData = {
      ...data,
      createBy: user.username,
    } as CreateInput;
    
    const result = await this.service.create(createData);
    return ResponseUtil.success(c, result, '创建成功');
  };

  /**
   * 更新记录
   */
  update = async (c: Context) => {
    const id = parseInt(c.req.param('id'));
    const data = await c.req.json() as UpdateInput;
    
    // 添加更新者信息
    const user = getCurrentUser(c);
    const updateData = {
      ...data,
      updateBy: user.username,
    } as UpdateInput;
    
    const result = await this.service.update(id, updateData);
    return ResponseUtil.success(c, result, '更新成功');
  };

  /**
   * 删除记录
   */
  remove = async (c: Context) => {
    const id = parseInt(c.req.param('id'));
    await this.service.delete(id);
    return ResponseUtil.success(c, null, '删除成功');
  };

  /**
   * 批量删除
   */
  removeMany = async (c: Context) => {
    const { ids } = await c.req.json() as { ids: number[] };
    await this.service.deleteMany(ids);
    return ResponseUtil.success(c, null, '批量删除成功');
  };

  /**
   * 获取统计信息
   */
  getStats = async (c: Context) => {
    const query = c.req.query();
    const total = await this.service.count(query);
    return ResponseUtil.success(c, { total }, '获取统计信息成功');
  };

  /**
   * 检查记录是否存在
   */
  checkExists = async (c: Context) => {
    const query = c.req.query();
    const exists = await this.service.exists(query);
    return ResponseUtil.success(c, { exists }, '检查完成');
  };

  /**
   * 导出数据
   */
  export = async (c: Context) => {
    const query = c.req.query();
    const data = await this.service.findMany(query);
    
    // 这里可以根据需要实现不同的导出格式
    // 例如 Excel、CSV 等
    return ResponseUtil.success(c, data, '导出成功');
  };

  /**
   * 导入数据
   */
  import = async (c: Context) => {
    // 这里可以实现文件上传和数据导入逻辑
    const data = await c.req.json() as CreateInput[];
    
    const results = await this.service.batchOperation(
      data,
      async (item) => this.service.create(item)
    );
    
    return ResponseUtil.success(c, results, '导入成功');
  };

  /**
   * 获取选项列表（用于下拉框等）
   */
  getOptions = async (c: Context) => {
    const data = await this.service.findMany({
      select: this.getOptionFields(),
      where: this.getOptionWhere(),
      orderBy: this.getOptionOrderBy(),
    });
    
    return ResponseUtil.success(c, data, '获取选项成功');
  };

  /**
   * 复制记录
   */
  copy = async (c: Context) => {
    const id = parseInt(c.req.param('id'));
    const original = await this.service.findById(id);
    
    // 移除ID和时间戳字段
    const copyData = this.prepareCopyData(original);
    
    const result = await this.service.create(copyData);
    return ResponseUtil.success(c, result, '复制成功');
  };

  /**
   * 批量更新状态
   */
  updateStatus = async (c: Context) => {
    const { ids, status } = await c.req.json() as { ids: number[]; status: string };
    
    const user = getCurrentUser(c);
    const updateData = {
      status,
      updateBy: user.username,
    } as Partial<UpdateInput>;
    
    await this.service.batchOperation(
      ids,
      async (id) => this.service.update(id, updateData as UpdateInput)
    );
    
    return ResponseUtil.success(c, null, '状态更新成功');
  };

  /**
   * 获取选项字段（子类可重写）
   */
  protected getOptionFields(): any {
    return {
      id: true,
      name: true,
    };
  }

  /**
   * 获取选项查询条件（子类可重写）
   */
  protected getOptionWhere(): any {
    return {
      status: '0', // 只返回启用的记录
    };
  }

  /**
   * 获取选项排序（子类可重写）
   */
  protected getOptionOrderBy(): any {
    return {
      sort: 'asc',
    };
  }

  /**
   * 准备复制数据（子类可重写）
   */
  protected prepareCopyData(original: T): CreateInput {
    const { id, createdAt, updatedAt, createBy, updateBy, ...copyData } = original as any;
    return {
      ...copyData,
      name: `${copyData.name}_副本`,
    } as CreateInput;
  }

  /**
   * 验证ID参数
   */
  protected validateId(c: Context): number {
    const id = parseInt(c.req.param('id'));
    if (isNaN(id) || id <= 0) {
      throw new Error('无效的ID参数');
    }
    return id;
  }

  /**
   * 获取查询参数
   */
  protected getQueryParams(c: Context): any {
    return c.req.query();
  }

  /**
   * 获取请求体数据
   */
  protected async getRequestBody<T>(c: Context): Promise<T> {
    return c.req.json() as Promise<T>;
  }

  /**
   * 获取当前用户
   */
  protected getCurrentUser(c: Context) {
    return getCurrentUser(c);
  }
}
