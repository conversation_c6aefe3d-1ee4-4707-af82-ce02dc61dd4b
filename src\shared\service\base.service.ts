// src/shared/service/base.service.ts
/**
 * 基础Service类
 */

import type { IBaseRepository } from '@/shared/repository/base.repository';
import type { PaginationQuery, PaginationResult } from '@/types/common';
import { NotFoundError, ConflictError } from '@/shared/errors';

/**
 * 基础Service接口
 */
export interface IBaseService<T, CreateInput, UpdateInput> {
  findById(id: number): Promise<T>;
  findMany(query?: any): Promise<T[]>;
  findWithPagination(query: PaginationQuery & any): Promise<PaginationResult<T>>;
  create(data: CreateInput): Promise<T>;
  update(id: number, data: UpdateInput): Promise<T>;
  delete(id: number): Promise<void>;
}

/**
 * 基础Service抽象类
 */
export abstract class BaseService<T, CreateInput, UpdateInput> 
  implements IBaseService<T, CreateInput, UpdateInput> {
  
  protected abstract repository: IBaseRepository<T, CreateInput, UpdateInput>;
  protected abstract entityName: string;

  /**
   * 根据ID查找记录
   */
  async findById(id: number): Promise<T> {
    const entity = await this.repository.findById(id);
    if (!entity) {
      throw new NotFoundError(this.entityName);
    }
    return entity;
  }

  /**
   * 查找多条记录
   */
  async findMany(query: any = {}): Promise<T[]> {
    return this.repository.findMany(query);
  }

  /**
   * 分页查询
   */
  async findWithPagination(query: PaginationQuery & any): Promise<PaginationResult<T>> {
    return this.repository.findWithPagination(query);
  }

  /**
   * 创建记录
   */
  async create(data: CreateInput): Promise<T> {
    // 创建前验证
    await this.validateBeforeCreate(data);
    
    // 执行创建
    const entity = await this.repository.create(data);
    
    // 创建后处理
    await this.afterCreate(entity);
    
    return entity;
  }

  /**
   * 更新记录
   */
  async update(id: number, data: UpdateInput): Promise<T> {
    // 检查记录是否存在
    const existingEntity = await this.findById(id);
    
    // 更新前验证
    await this.validateBeforeUpdate(id, data, existingEntity);
    
    // 执行更新
    const entity = await this.repository.update(id, data);
    
    // 更新后处理
    await this.afterUpdate(entity, existingEntity);
    
    return entity;
  }

  /**
   * 删除记录
   */
  async delete(id: number): Promise<void> {
    // 检查记录是否存在
    const existingEntity = await this.findById(id);
    
    // 删除前验证
    await this.validateBeforeDelete(id, existingEntity);
    
    // 执行删除
    if (this.repository.softDelete) {
      await this.repository.softDelete(id);
    } else {
      await this.repository.delete(id);
    }
    
    // 删除后处理
    await this.afterDelete(existingEntity);
  }

  /**
   * 批量删除
   */
  async deleteMany(ids: number[]): Promise<void> {
    for (const id of ids) {
      await this.delete(id);
    }
  }

  /**
   * 检查记录是否存在
   */
  async exists(where: any): Promise<boolean> {
    return this.repository.exists(where);
  }

  /**
   * 计数
   */
  async count(where: any = {}): Promise<number> {
    return this.repository.count(where);
  }

  /**
   * 创建前验证（子类可重写）
   */
  protected async validateBeforeCreate(data: CreateInput): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 更新前验证（子类可重写）
   */
  protected async validateBeforeUpdate(
    id: number, 
    data: UpdateInput, 
    existingEntity: T
  ): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 删除前验证（子类可重写）
   */
  protected async validateBeforeDelete(id: number, existingEntity: T): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 创建后处理（子类可重写）
   */
  protected async afterCreate(entity: T): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 更新后处理（子类可重写）
   */
  protected async afterUpdate(entity: T, previousEntity: T): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 删除后处理（子类可重写）
   */
  protected async afterDelete(entity: T): Promise<void> {
    // 默认实现为空，子类可重写
  }

  /**
   * 检查唯一性约束
   */
  protected async checkUniqueness(
    field: string, 
    value: any, 
    excludeId?: number
  ): Promise<void> {
    const where: any = { [field]: value };
    if (excludeId) {
      where.id = { not: excludeId };
    }
    
    const exists = await this.repository.exists(where);
    if (exists) {
      throw new ConflictError(`${field} 已存在`);
    }
  }

  /**
   * 事务执行
   */
  async transaction<R>(fn: (service: this) => Promise<R>): Promise<R> {
    return this.repository.transaction(async (tx) => {
      // 创建一个新的service实例，使用事务连接
      const transactionalService = Object.create(this);
      transactionalService.repository = { ...this.repository, db: tx };
      return fn(transactionalService);
    });
  }

  /**
   * 批量操作
   */
  async batchOperation<R>(
    items: any[], 
    operation: (item: any, index: number) => Promise<R>
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i++) {
      const result = await operation(items[i], i);
      results.push(result);
    }
    
    return results;
  }

  /**
   * 并行批量操作
   */
  async parallelBatchOperation<R>(
    items: any[], 
    operation: (item: any, index: number) => Promise<R>,
    concurrency: number = 5
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += concurrency) {
      const batch = items.slice(i, i + concurrency);
      const batchPromises = batch.map((item, index) => 
        operation(item, i + index)
      );
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }
    
    return results;
  }
}
