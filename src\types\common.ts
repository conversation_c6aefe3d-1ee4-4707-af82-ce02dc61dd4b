// src/types/common.ts
/**
 * 通用类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  timestamp?: string;
}

// 分页请求参数
export interface PaginationQuery {
  pageNum?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// 分页响应数据
export interface PaginationResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 分页响应
export interface PaginatedApiResponse<T> extends ApiResponse<PaginationResult<T>> {}

// 基础实体字段
export interface BaseEntity {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createBy?: string;
  updateBy?: string;
  remark?: string;
}

// 软删除实体
export interface SoftDeleteEntity extends BaseEntity {
  delFlag: string;
}

// 状态实体
export interface StatusEntity extends BaseEntity {
  status: string;
}

// 排序实体
export interface SortableEntity {
  orderNo?: number;
  sort?: number;
}

// 树形结构实体
export interface TreeEntity {
  parentId?: number | null;
  children?: TreeEntity[];
}

// 请求上下文用户信息
export interface RequestUser {
  id: number;
  username: string;
  nickname?: string;
  roles: string[];
  permissions: string[];
}

// 文件上传信息
export interface FileInfo {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  path: string;
  url: string;
}

// 操作日志类型
export interface OperationLog {
  id: number;
  userId: number;
  username: string;
  operation: string;
  method: string;
  params?: string;
  time: number;
  ip: string;
  location?: string;
  userAgent?: string;
  createdAt: Date;
}

// 登录日志类型
export interface LoginLog {
  id: number;
  userId?: number;
  username: string;
  ip: string;
  location?: string;
  browser?: string;
  os?: string;
  status: 'success' | 'failed';
  message?: string;
  createdAt: Date;
}

// 错误码枚举
export enum ErrorCode {
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
  
  // 业务错误码
  USER_NOT_FOUND = 1001,
  USER_DISABLED = 1002,
  INVALID_PASSWORD = 1003,
  CAPTCHA_ERROR = 1004,
  TOKEN_EXPIRED = 1005,
  PERMISSION_DENIED = 1006,
  
  // 数据错误码
  DATA_NOT_FOUND = 2001,
  DATA_ALREADY_EXISTS = 2002,
  DATA_CONSTRAINT_VIOLATION = 2003,
  
  // 系统错误码
  DATABASE_ERROR = 3001,
  REDIS_ERROR = 3002,
  FILE_UPLOAD_ERROR = 3003,
}

// 错误消息映射
export const ErrorMessages: Record<ErrorCode, string> = {
  [ErrorCode.SUCCESS]: '操作成功',
  [ErrorCode.BAD_REQUEST]: '请求参数错误',
  [ErrorCode.UNAUTHORIZED]: '未授权，请先登录',
  [ErrorCode.FORBIDDEN]: '权限不足',
  [ErrorCode.NOT_FOUND]: '资源不存在',
  [ErrorCode.INTERNAL_ERROR]: '服务器内部错误',
  
  [ErrorCode.USER_NOT_FOUND]: '用户不存在',
  [ErrorCode.USER_DISABLED]: '用户已被禁用',
  [ErrorCode.INVALID_PASSWORD]: '用户名或密码错误',
  [ErrorCode.CAPTCHA_ERROR]: '验证码错误或已过期',
  [ErrorCode.TOKEN_EXPIRED]: 'Token已过期',
  [ErrorCode.PERMISSION_DENIED]: '权限不足',
  
  [ErrorCode.DATA_NOT_FOUND]: '数据不存在',
  [ErrorCode.DATA_ALREADY_EXISTS]: '数据已存在',
  [ErrorCode.DATA_CONSTRAINT_VIOLATION]: '数据约束冲突',
  
  [ErrorCode.DATABASE_ERROR]: '数据库操作失败',
  [ErrorCode.REDIS_ERROR]: 'Redis操作失败',
  [ErrorCode.FILE_UPLOAD_ERROR]: '文件上传失败',
};

// 环境类型
export type Environment = 'development' | 'production' | 'test';

// 日志级别
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

// 性别枚举
export enum Gender {
  MALE = '0',
  FEMALE = '1',
  UNKNOWN = '2',
}

// 状态枚举
export enum Status {
  ENABLED = '0',
  DISABLED = '1',
}

// 删除标志枚举
export enum DelFlag {
  NORMAL = '0',
  DELETED = '1',
}

// 菜单类型枚举
export enum MenuType {
  DIRECTORY = 0,
  MENU = 1,
  BUTTON = 2,
}

// 是否枚举
export enum YesNo {
  NO = 0,
  YES = 1,
}
